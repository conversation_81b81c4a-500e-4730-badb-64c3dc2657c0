# WhatsApp Cart Checkout Button Replacement Guide

## Overview
The WhatsApp Order Button plugin now supports replacing the default "Proceed to Checkout" button on the cart page with a WhatsApp order button.

## How to Enable Checkout Button Replacement

### Step 1: Access Plugin Settings
1. Go to your WordPress admin dashboard
2. Navigate to **WooCommerce → WhatsApp Order Settings**
3. Click on the **"Display"** tab

### Step 2: Enable the Feature
1. Find the **"Replace Checkout Button"** option
2. Check the checkbox to enable it
3. Click **"Save Changes"**

## What Happens When Enabled

### Before (Default WooCommerce):
- Cart page shows "Proceed to Checkout" button
- Clicking takes users to checkout page

### After (WhatsApp Replacement):
- The "Proceed to Checkout" button is hidden
- A WhatsApp order button appears in its place
- Clicking opens WhatsApp with cart details pre-filled

## Cart Message Format

When customers click the WhatsApp button, they'll get a message like:

```
Hi! I would like to order the following items:

Short Sleeve T-Shirt
Quantity: 1
Price: ₹30.00

Total: ₹30.00

Cart Link: https://yoursite.com/cart
```

## Customization Options

### Button Text
- Go to **General** tab in plugin settings
- Change **"Button Text"** field
- Default: "Order on WhatsApp"

### Button Color
- Go to **Appearance** tab in plugin settings
- Modify **"Button Color"** and **"Button Text Color"**
- Default: WhatsApp green (#25D366)

### WhatsApp Number
- Go to **General** tab in plugin settings
- Set your **"WhatsApp Number"** with country code
- Example: +1234567890

## CSS Customization

The replacement button uses these CSS classes:
- `.whatsapp-checkout-replacement` - Container div
- `.whatsapp-order-button-checkout` - The button itself

You can add custom CSS in the plugin settings under **Appearance → Custom CSS**.

## Mobile Responsiveness

The button automatically adapts to mobile devices:
- Full width on mobile screens
- Maintains proper spacing and sizing
- Touch-friendly button size

## Troubleshooting

### Button Not Showing
1. Check if plugin is enabled in settings
2. Verify WhatsApp number is set
3. Ensure "Replace Checkout Button" is checked
4. Clear any caching plugins

### Button Styling Issues
1. Check for theme conflicts
2. Add custom CSS if needed
3. Ensure no other plugins are interfering

### WhatsApp Not Opening
1. Verify WhatsApp number format (+countrycode)
2. Test on different devices
3. Check browser compatibility

## Technical Notes

- The original checkout button is hidden with CSS
- WhatsApp button is added via WordPress hooks
- Cart data is automatically formatted for WhatsApp
- Works with variable products and cart variations
- Compatible with most WooCommerce themes

## Support

If you encounter any issues:
1. Check the debug helper plugin (wp-debug-helper.php)
2. Enable WordPress debug mode
3. Check error logs for specific issues
4. Ensure all plugin files are properly uploaded
