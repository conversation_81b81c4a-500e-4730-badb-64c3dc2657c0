<?php
/**
 * WhatsApp Order Button Admin Class
 *
 * @package WhatsApp_Order_Button
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WhatsApp Order Button Admin Class
 */
class WhatsApp_Order_Button_Admin {

    /**
     * Single instance of the class
     *
     * @var WhatsApp_Order_Button_Admin
     */
    private static $instance = null;

    /**
     * Get single instance of the class
     *
     * @return WhatsApp_Order_Button_Admin
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_product_fields'));
        add_action('woocommerce_process_product_meta', array($this, 'save_product_fields'));
        add_action('product_cat_add_form_fields', array($this, 'add_category_fields'));
        add_action('product_cat_edit_form_fields', array($this, 'edit_category_fields'));
        add_action('edited_product_cat', array($this, 'save_category_fields'));
        add_action('create_product_cat', array($this, 'save_category_fields'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('WhatsApp Order Settings', 'whatsapp-order-button'),
            __('WhatsApp Order', 'whatsapp-order-button'),
            'manage_woocommerce',
            'whatsapp-order-settings',
            array($this, 'admin_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('whatsapp_order_button_settings', 'whatsapp_order_button_settings');
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }

        $settings = WhatsApp_Order_Button::get_instance()->get_settings();
        include WHATSAPP_ORDER_BUTTON_PLUGIN_DIR . 'admin/admin-settings.php';
    }

    /**
     * Save settings
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['whatsapp_order_nonce'], 'whatsapp_order_settings')) {
            wp_die(__('Security check failed', 'whatsapp-order-button'));
        }

        $settings = array(
            'enabled' => sanitize_text_field(isset($_POST['enabled']) ? $_POST['enabled'] : 'no'),
            'whatsapp_number' => sanitize_text_field(isset($_POST['whatsapp_number']) ? $_POST['whatsapp_number'] : ''),
            'button_text' => sanitize_text_field(isset($_POST['button_text']) ? $_POST['button_text'] : __('Order on WhatsApp', 'whatsapp-order-button')),
            'button_position' => sanitize_text_field(isset($_POST['button_position']) ? $_POST['button_position'] : 'after_add_to_cart'),
            'disable_add_to_cart' => sanitize_text_field(isset($_POST['disable_add_to_cart']) ? $_POST['disable_add_to_cart'] : 'no'),
            'message_template' => wp_kses_post(isset($_POST['message_template']) ? $_POST['message_template'] : ''),
            'button_color' => sanitize_hex_color(isset($_POST['button_color']) ? $_POST['button_color'] : '#25D366'),
            'button_text_color' => sanitize_hex_color(isset($_POST['button_text_color']) ? $_POST['button_text_color'] : '#ffffff'),
            'button_size' => sanitize_text_field(isset($_POST['button_size']) ? $_POST['button_size'] : 'medium'),
            'show_on_shop' => sanitize_text_field(isset($_POST['show_on_shop']) ? $_POST['show_on_shop'] : 'no'),
            'show_on_cart' => sanitize_text_field(isset($_POST['show_on_cart']) ? $_POST['show_on_cart'] : 'yes'),
            'show_on_checkout' => sanitize_text_field(isset($_POST['show_on_checkout']) ? $_POST['show_on_checkout'] : 'yes'),
            'replace_checkout_button' => sanitize_text_field(isset($_POST['replace_checkout_button']) ? $_POST['replace_checkout_button'] : 'no'),
            'device_behavior' => sanitize_text_field(isset($_POST['device_behavior']) ? $_POST['device_behavior'] : 'auto'),
            'enable_analytics' => sanitize_text_field(isset($_POST['enable_analytics']) ? $_POST['enable_analytics'] : 'no'),
            'floating_button' => sanitize_text_field(isset($_POST['floating_button']) ? $_POST['floating_button'] : 'no'),
            'floating_position' => sanitize_text_field(isset($_POST['floating_position']) ? $_POST['floating_position'] : 'bottom-right'),
            'show_size_guide' => sanitize_text_field(isset($_POST['show_size_guide']) ? $_POST['show_size_guide'] : 'no'),
            'size_guide_text' => sanitize_text_field(isset($_POST['size_guide_text']) ? $_POST['size_guide_text'] : __('Size Guide', 'whatsapp-order-button')),
            'custom_css' => wp_strip_all_tags(isset($_POST['custom_css']) ? $_POST['custom_css'] : ''),
            'exclude_categories' => array_map('intval', isset($_POST['exclude_categories']) ? $_POST['exclude_categories'] : array()),
            'exclude_products' => array_map('intval', isset($_POST['exclude_products']) ? $_POST['exclude_products'] : array()),
            // New URL customization settings
            'enable_custom_urls' => sanitize_text_field(isset($_POST['enable_custom_urls']) ? $_POST['enable_custom_urls'] : 'no'),
            'custom_url_template' => sanitize_textarea_field(isset($_POST['custom_url_template']) ? $_POST['custom_url_template'] : ''),
            'custom_url_fallback' => sanitize_text_field(isset($_POST['custom_url_fallback']) ? $_POST['custom_url_fallback'] : 'whatsapp')
        );

        WhatsApp_Order_Button::get_instance()->update_settings($settings);

        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'whatsapp-order-button') . '</p></div>';
        });
    }

    /**
     * Add product fields
     */
    public function add_product_fields() {
        global $post;

        echo '<div class="options_group">';
        
        woocommerce_wp_text_input(array(
            'id' => '_whatsapp_number',
            'label' => __('WhatsApp Number', 'whatsapp-order-button'),
            'description' => __('Override global WhatsApp number for this product', 'whatsapp-order-button'),
            'desc_tip' => true,
            'placeholder' => '+1234567890'
        ));

        woocommerce_wp_checkbox(array(
            'id' => '_disable_add_to_cart',
            'label' => __('Disable Add to Cart', 'whatsapp-order-button'),
            'description' => __('Disable add to cart for this product', 'whatsapp-order-button')
        ));

        woocommerce_wp_checkbox(array(
            'id' => '_hide_whatsapp_button',
            'label' => __('Hide WhatsApp Button', 'whatsapp-order-button'),
            'description' => __('Hide WhatsApp button for this product', 'whatsapp-order-button')
        ));

        woocommerce_wp_textarea_input(array(
            'id' => '_custom_whatsapp_url',
            'label' => __('Custom WhatsApp URL', 'whatsapp-order-button'),
            'description' => __('Custom URL template for this product. Use placeholders like {product_name}, {price}, etc.', 'whatsapp-order-button'),
            'desc_tip' => true,
            'placeholder' => 'https://wa.me/{whatsapp_number}?text={encoded_message}',
            'rows' => 3
        ));

        echo '</div>';
    }

    /**
     * Save product fields
     */
    public function save_product_fields($post_id) {
        $whatsapp_number = sanitize_text_field(isset($_POST['_whatsapp_number']) ? $_POST['_whatsapp_number'] : '');
        $disable_add_to_cart = isset($_POST['_disable_add_to_cart']) ? 'yes' : 'no';
        $hide_whatsapp_button = isset($_POST['_hide_whatsapp_button']) ? 'yes' : 'no';
        $custom_whatsapp_url = sanitize_textarea_field(isset($_POST['_custom_whatsapp_url']) ? $_POST['_custom_whatsapp_url'] : '');

        update_post_meta($post_id, '_whatsapp_number', $whatsapp_number);
        update_post_meta($post_id, '_disable_add_to_cart', $disable_add_to_cart);
        update_post_meta($post_id, '_hide_whatsapp_button', $hide_whatsapp_button);
        update_post_meta($post_id, '_custom_whatsapp_url', $custom_whatsapp_url);
    }

    /**
     * Add category fields
     */
    public function add_category_fields() {
        ?>
        <div class="form-field">
            <label for="whatsapp_number"><?php _e('WhatsApp Number', 'whatsapp-order-button'); ?></label>
            <input type="text" name="whatsapp_number" id="whatsapp_number" placeholder="+1234567890" />
            <p class="description"><?php _e('Override global WhatsApp number for this category', 'whatsapp-order-button'); ?></p>
        </div>
        <div class="form-field">
            <label for="disable_add_to_cart">
                <input type="checkbox" name="disable_add_to_cart" id="disable_add_to_cart" value="yes" />
                <?php _e('Disable Add to Cart for this category', 'whatsapp-order-button'); ?>
            </label>
        </div>
        <?php
    }

    /**
     * Edit category fields
     */
    public function edit_category_fields($term) {
        $whatsapp_number = get_term_meta($term->term_id, 'whatsapp_number', true);
        $disable_add_to_cart = get_term_meta($term->term_id, 'disable_add_to_cart', true);
        ?>
        <tr class="form-field">
            <th scope="row" valign="top"><label for="whatsapp_number"><?php _e('WhatsApp Number', 'whatsapp-order-button'); ?></label></th>
            <td>
                <input type="text" name="whatsapp_number" id="whatsapp_number" value="<?php echo esc_attr($whatsapp_number); ?>" placeholder="+1234567890" />
                <p class="description"><?php _e('Override global WhatsApp number for this category', 'whatsapp-order-button'); ?></p>
            </td>
        </tr>
        <tr class="form-field">
            <th scope="row" valign="top"><label for="disable_add_to_cart"><?php _e('Disable Add to Cart', 'whatsapp-order-button'); ?></label></th>
            <td>
                <label for="disable_add_to_cart">
                    <input type="checkbox" name="disable_add_to_cart" id="disable_add_to_cart" value="yes" <?php checked($disable_add_to_cart, 'yes'); ?> />
                    <?php _e('Disable Add to Cart for this category', 'whatsapp-order-button'); ?>
                </label>
            </td>
        </tr>
        <?php
    }

    /**
     * Save category fields
     */
    public function save_category_fields($term_id) {
        $whatsapp_number = sanitize_text_field(isset($_POST['whatsapp_number']) ? $_POST['whatsapp_number'] : '');
        $disable_add_to_cart = isset($_POST['disable_add_to_cart']) ? 'yes' : 'no';

        update_term_meta($term_id, 'whatsapp_number', $whatsapp_number);
        update_term_meta($term_id, 'disable_add_to_cart', $disable_add_to_cart);
    }
}
