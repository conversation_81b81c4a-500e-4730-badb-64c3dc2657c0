# Copyright (C) 2024 WhatsApp Order Button for WooCommerce - Clothing Store Edition
# This file is distributed under the same license as the WhatsApp Order Button for WooCommerce - Clothing Store Edition package.
msgid ""
msgstr ""
"Project-Id-Version: WhatsApp Order Button for WooCommerce - Clothing Store Edition 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/whatsapp-order-button\n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: whatsapp-order-button.php:45
msgid "WhatsApp Order Button requires WooCommerce to be installed and active."
msgstr ""

#: whatsapp-order-button.php:74
msgid "WhatsApp Order Button requires WooCommerce to be installed and active."
msgstr ""

#: whatsapp-order-button.php:82
msgid "Order on WhatsApp"
msgstr ""

#: whatsapp-order-button.php:86
msgid "Hi! I would like to order:\\n\\nProduct: {product_name}\\nPrice: {price}\\nQuantity: {quantity}\\n{variations}\\n\\nProduct Link: {product_url}"
msgstr ""

#: includes/class-whatsapp-admin.php:49
msgid "WhatsApp Order Settings"
msgstr ""

#: includes/class-whatsapp-admin.php:50
msgid "WhatsApp Order"
msgstr ""

#: includes/class-whatsapp-admin.php:78
msgid "Security check failed"
msgstr ""

#: includes/class-whatsapp-admin.php:85
msgid "Order on WhatsApp"
msgstr ""

#: includes/class-whatsapp-admin.php:103
msgid "Size Guide"
msgstr ""

#: includes/class-whatsapp-admin.php:109
msgid "Settings saved successfully!"
msgstr ""

#: includes/class-whatsapp-admin.php:119
msgid "WhatsApp Number"
msgstr ""

#: includes/class-whatsapp-admin.php:120
msgid "Override global WhatsApp number for this product"
msgstr ""

#: includes/class-whatsapp-admin.php:126
msgid "Disable Add to Cart"
msgstr ""

#: includes/class-whatsapp-admin.php:127
msgid "Disable add to cart for this product"
msgstr ""

#: includes/class-whatsapp-admin.php:132
msgid "Hide WhatsApp Button"
msgstr ""

#: includes/class-whatsapp-admin.php:133
msgid "Hide WhatsApp button for this product"
msgstr ""

#: includes/class-whatsapp-admin.php:150
msgid "WhatsApp Number"
msgstr ""

#: includes/class-whatsapp-admin.php:152
msgid "Override global WhatsApp number for this category"
msgstr ""

#: includes/class-whatsapp-admin.php:156
msgid "Disable Add to Cart for this category"
msgstr ""

#: includes/class-whatsapp-admin.php:165
msgid "WhatsApp Number"
msgstr ""

#: includes/class-whatsapp-admin.php:168
msgid "Override global WhatsApp number for this category"
msgstr ""

#: includes/class-whatsapp-admin.php:173
msgid "Disable Add to Cart"
msgstr ""

#: includes/class-whatsapp-admin.php:177
msgid "Disable Add to Cart for this category"
msgstr ""

#: includes/class-whatsapp-frontend.php:118
msgid "Prefer to order via WhatsApp?"
msgstr ""

#: includes/class-whatsapp-frontend.php:135
msgid "Hi! I would like to know more about your products."
msgstr ""

#: includes/class-whatsapp-frontend.php:183
msgid "Size Guide"
msgstr ""

#: includes/class-whatsapp-frontend.php:207
msgid "Hi! I would like to order the following items:"
msgstr ""

#: includes/class-whatsapp-frontend.php:215
msgid "Quantity"
msgstr ""

#: includes/class-whatsapp-frontend.php:217
msgid "Price"
msgstr ""

#: includes/class-whatsapp-frontend.php:230
msgid "Total"
msgstr ""

#: includes/class-whatsapp-frontend.php:233
msgid "Cart Link"
msgstr ""

#: admin/admin-settings.php:15
msgid "WhatsApp Order Button Settings"
msgstr ""

#: admin/admin-settings.php:20
msgid "General"
msgstr ""

#: admin/admin-settings.php:21
msgid "Appearance"
msgstr ""

#: admin/admin-settings.php:22
msgid "Display Options"
msgstr ""

#: admin/admin-settings.php:23
msgid "Advanced"
msgstr ""

#: admin/admin-settings.php:27
msgid "General Settings"
msgstr ""

#: admin/admin-settings.php:31
msgid "Enable Plugin"
msgstr ""

#: admin/admin-settings.php:34
msgid "Enable WhatsApp Order Button"
msgstr ""

#: admin/admin-settings.php:39
msgid "WhatsApp Number"
msgstr ""

#: admin/admin-settings.php:42
msgid "Enter your WhatsApp number with country code (e.g., +1234567890)"
msgstr ""

#: admin/admin-settings.php:46
msgid "Button Text"
msgstr ""

#: admin/admin-settings.php:51
msgid "Message Template"
msgstr ""

#: admin/admin-settings.php:54
msgid "Available placeholders:"
msgstr ""

#: admin/admin-settings.php:59
msgid "Disable Add to Cart"
msgstr ""

#: admin/admin-settings.php:62
msgid "Disable Add to Cart globally (force WhatsApp ordering)"
msgstr ""

#: admin/admin-settings.php:69
msgid "Appearance Settings"
msgstr ""

#: admin/admin-settings.php:73
msgid "Button Color"
msgstr ""

#: admin/admin-settings.php:79
msgid "Button Text Color"
msgstr ""

#: admin/admin-settings.php:85
msgid "Button Size"
msgstr ""

#: admin/admin-settings.php:88
msgid "Small"
msgstr ""

#: admin/admin-settings.php:89
msgid "Medium"
msgstr ""

#: admin/admin-settings.php:90
msgid "Large"
msgstr ""

#: admin/admin-settings.php:95
msgid "Button Position"
msgstr ""

#: admin/admin-settings.php:98
msgid "Before Add to Cart"
msgstr ""

#: admin/admin-settings.php:99
msgid "After Add to Cart"
msgstr ""

#: admin/admin-settings.php:100
msgid "Replace Add to Cart"
msgstr ""

#: admin/admin-settings.php:105
msgid "Custom CSS"
msgstr ""

#: admin/admin-settings.php:108
msgid "Add custom CSS to style the WhatsApp button"
msgstr ""

#: admin/admin-settings.php:115
msgid "Display Options"
msgstr ""

#: admin/admin-settings.php:119
msgid "Show on Shop Page"
msgstr ""

#: admin/admin-settings.php:122
msgid "Show WhatsApp button on shop/archive pages"
msgstr ""

#: admin/admin-settings.php:127
msgid "Show on Cart Page"
msgstr ""

#: admin/admin-settings.php:130
msgid "Show WhatsApp button on cart page"
msgstr ""

#: admin/admin-settings.php:135
msgid "Show on Checkout Page"
msgstr ""

#: admin/admin-settings.php:138
msgid "Show WhatsApp button on checkout page"
msgstr ""

#: admin/admin-settings.php:143
msgid "Floating Button"
msgstr ""

#: admin/admin-settings.php:146
msgid "Show floating WhatsApp button"
msgstr ""

#: admin/admin-settings.php:151
msgid "Floating Button Position"
msgstr ""

#: admin/admin-settings.php:154
msgid "Bottom Right"
msgstr ""

#: admin/admin-settings.php:155
msgid "Bottom Left"
msgstr ""

#: admin/admin-settings.php:156
msgid "Top Right"
msgstr ""

#: admin/admin-settings.php:157
msgid "Top Left"
msgstr ""

#: admin/admin-settings.php:162
msgid "Size Guide"
msgstr ""

#: admin/admin-settings.php:165
msgid "Show size guide link for clothing products"
msgstr ""

#: admin/admin-settings.php:169
msgid "Size Guide"
msgstr ""

#: admin/admin-settings.php:170
msgid "Text for the size guide link"
msgstr ""

#: admin/admin-settings.php:177
msgid "Advanced Settings"
msgstr ""

#: admin/admin-settings.php:181
msgid "Device Behavior"
msgstr ""

#: admin/admin-settings.php:184
msgid "Auto (App on mobile, Web on desktop)"
msgstr ""

#: admin/admin-settings.php:185
msgid "Always use WhatsApp App"
msgstr ""

#: admin/admin-settings.php:186
msgid "Always use WhatsApp Web"
msgstr ""

#: admin/admin-settings.php:191
msgid "Enable Analytics"
msgstr ""

#: admin/admin-settings.php:194
msgid "Track button clicks for analytics"
msgstr ""

#: admin/admin-settings.php:199
msgid "Exclude Categories"
msgstr ""

#: admin/admin-settings.php:212
msgid "Select categories where WhatsApp button should not appear"
msgstr ""

#: public/js/whatsapp-order.js:45
msgid "Please select product options before ordering."
msgstr ""

#: public/js/whatsapp-order.js:46
msgid "Please enter a valid quantity."
msgstr ""
