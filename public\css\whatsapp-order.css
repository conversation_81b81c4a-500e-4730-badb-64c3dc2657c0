/**
 * WhatsApp Order But<PERSON> Styles
 */

/* Main <PERSON><PERSON> Styles */
.whatsapp-order-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: #25D366;
    color: #ffffff;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);
    margin: 10px 0;
    min-width: 200px;
}

.whatsapp-order-button:hover {
    background-color: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    color: #ffffff;
    text-decoration: none;
}

.whatsapp-order-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
}

/* <PERSON><PERSON> Sizes */
.whatsapp-size-small {
    padding: 8px 16px;
    font-size: 14px;
    min-width: 150px;
}

.whatsapp-size-medium {
    padding: 12px 20px;
    font-size: 16px;
    min-width: 200px;
}

.whatsapp-size-large {
    padding: 16px 24px;
    font-size: 18px;
    min-width: 250px;
}

/* Icon Styles */
.whatsapp-icon {
    font-size: 1.2em;
    line-height: 1;
}

/* Button Wrapper */
.whatsapp-order-button-wrapper {
    margin: 15px 0;
    text-align: center;
}

/* Size Guide Link */
.whatsapp-size-guide-link {
    display: inline-block;
    margin-bottom: 10px;
    color: #666;
    text-decoration: underline;
    font-size: 14px;
}

.whatsapp-size-guide-link:hover {
    color: #25D366;
}

/* Shop Page Button */
.whatsapp-order-shop {
    width: 100%;
    margin-top: 10px;
}

/* Cart Page Button */
.whatsapp-order-cart-button {
    margin: 20px 0;
    text-align: center;
}

.whatsapp-order-cart-button .whatsapp-order-button {
    width: 100%;
    max-width: 300px;
}

/* Checkout Page */
.whatsapp-order-checkout-notice {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.whatsapp-order-checkout-notice p {
    margin-bottom: 15px;
    font-weight: 600;
    color: #495057;
}

/* Checkout Replacement Button */
.whatsapp-checkout-replacement {
    margin: 20px 0;
    text-align: center;
}

.whatsapp-order-button-checkout {
    background-color: #25D366 !important;
    color: white !important;
    border: none !important;
    padding: 15px 30px !important;
    font-size: 16px !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    display: inline-block !important;
    width: 100% !important;
    max-width: 300px !important;
    font-weight: 600 !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.whatsapp-order-button-checkout:hover {
    background-color: #128C7E !important;
    color: white !important;
    text-decoration: none !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.whatsapp-order-button-checkout .whatsapp-icon {
    margin-right: 10px;
    font-size: 1.2em;
}

/* Hide default checkout button when replacement is active */
.whatsapp-checkout-replacement-active .wc-proceed-to-checkout .checkout-button,
.whatsapp-checkout-replacement-active .wc-proceed-to-checkout a.checkout-button {
    display: none !important;
}

/* Floating Button */
.whatsapp-floating-button {
    position: fixed;
    z-index: 9999;
    width: 60px;
    height: 60px;
}

.whatsapp-floating-button a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #25D366;
    border-radius: 50%;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    text-decoration: none;
}

.whatsapp-floating-button a:hover {
    background-color: #128C7E;
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.5);
}

.whatsapp-floating-button .whatsapp-icon {
    font-size: 24px;
    color: white;
}

/* Floating Button Positions */
.whatsapp-floating-bottom-right {
    bottom: 20px;
    right: 20px;
}

.whatsapp-floating-bottom-left {
    bottom: 20px;
    left: 20px;
}

.whatsapp-floating-top-right {
    top: 20px;
    right: 20px;
}

.whatsapp-floating-top-left {
    top: 20px;
    left: 20px;
}

/* Variable Product Styles */
.whatsapp-variable-product.whatsapp-order-button {
    opacity: 0.7;
    cursor: not-allowed;
}

.whatsapp-variable-product.whatsapp-order-button.enabled {
    opacity: 1;
    cursor: pointer;
}

/* Loading State */
.whatsapp-order-button.loading {
    opacity: 0.7;
    pointer-events: none;
}

.whatsapp-order-button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: whatsapp-spin 1s linear infinite;
}

@keyframes whatsapp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.whatsapp-order-error {
    background-color: #dc3545;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    text-align: center;
    font-size: 14px;
}

/* Success State */
.whatsapp-order-success {
    background-color: #28a745;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    text-align: center;
    font-size: 14px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .whatsapp-order-button {
        width: 100%;
        margin: 10px 0;
    }

    .whatsapp-floating-button {
        width: 50px;
        height: 50px;
    }

    .whatsapp-floating-button .whatsapp-icon {
        font-size: 20px;
    }

    .whatsapp-floating-bottom-right,
    .whatsapp-floating-bottom-left {
        bottom: 15px;
    }

    .whatsapp-floating-bottom-right {
        right: 15px;
    }

    .whatsapp-floating-bottom-left {
        left: 15px;
    }

    /* Shortcode responsive styles */
    .whatsapp-shortcode-button,
    .whatsapp-cart-shortcode-button,
    .whatsapp-contact-shortcode-button {
        display: block;
        width: 100%;
        max-width: 300px;
        margin: 10px auto;
    }

    .shortcode-output-wrapper {
        flex-direction: column;
    }

    .shortcode-output-wrapper textarea {
        margin-bottom: 10px;
    }

    .shortcode-generator-wrapper {
        padding: 15px;
    }

    .shortcode-reference {
        padding: 15px;
    }
}

/* RTL Support */
[dir="rtl"] .whatsapp-order-button {
    direction: rtl;
}

[dir="rtl"] .whatsapp-floating-bottom-right {
    right: auto;
    left: 20px;
}

[dir="rtl"] .whatsapp-floating-bottom-left {
    left: auto;
    right: 20px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .whatsapp-order-button {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .whatsapp-order-button,
    .whatsapp-floating-button a {
        transition: none;
    }
    
    .whatsapp-order-button:hover {
        transform: none;
    }
    
    .whatsapp-floating-button a:hover {
        transform: none;
    }
    
    @keyframes whatsapp-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }
}

/* Shortcode Button Styles */
.whatsapp-shortcode-button-wrapper,
.whatsapp-cart-shortcode-wrapper,
.whatsapp-contact-shortcode-wrapper {
    margin: 15px 0;
    text-align: center;
}

.whatsapp-shortcode-button,
.whatsapp-cart-shortcode-button,
.whatsapp-contact-shortcode-button {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: inherit;
}

.whatsapp-shortcode-button:hover,
.whatsapp-cart-shortcode-button:hover,
.whatsapp-contact-shortcode-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
    text-decoration: none;
}

/* Button Styles */
.whatsapp-style-inline {
    display: inline-block;
    margin: 0 5px;
}

.whatsapp-style-floating {
    position: fixed;
    z-index: 9999;
    border-radius: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.whatsapp-style-minimal {
    background: transparent !important;
    border: 2px solid #25D366;
    color: #25D366 !important;
}

.whatsapp-style-minimal:hover {
    background: #25D366 !important;
    color: white !important;
}

/* Floating Button Shortcode */
.whatsapp-floating-shortcode {
    position: fixed;
    z-index: 9999;
    border-radius: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.whatsapp-floating-shortcode:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.whatsapp-floating-shortcode a {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    min-height: 60px;
    border-radius: 50px;
}

/* Error Messages */
.whatsapp-error {
    color: #d63638;
    font-weight: 600;
    padding: 8px 12px;
    background: #fcf0f1;
    border: 1px solid #d63638;
    border-radius: 4px;
    display: inline-block;
}

.whatsapp-cart-empty {
    color: #646970;
    font-style: italic;
    text-align: center;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
}

/* Admin Shortcode Generator Styles */
.shortcode-generator-wrapper {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.shortcode-output-section {
    margin-top: 20px;
    padding: 15px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.shortcode-output-wrapper {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.shortcode-output-wrapper textarea {
    flex: 1;
    font-family: monospace;
    background: #f6f7f7;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
}

.shortcode-preview-section {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.shortcode-preview-area {
    padding: 20px;
    background: #f6f7f7;
    border-radius: 4px;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.shortcode-reference {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.shortcode-reference h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #23282d;
}

.shortcode-reference h4:first-child {
    margin-top: 0;
}

.shortcode-reference code {
    background: #23282d;
    color: #f1f1f1;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 13px;
}

.shortcode-reference p {
    margin: 5px 0;
    color: #646970;
}

/* Print Styles */
@media print {
    .whatsapp-order-button,
    .whatsapp-floating-button,
    .whatsapp-shortcode-button,
    .whatsapp-cart-shortcode-button,
    .whatsapp-contact-shortcode-button,
    .whatsapp-floating-shortcode {
        display: none;
    }
}
