# WhatsApp Order But<PERSON> for WooCommerce - Installation & Setup Guide

## 📋 Requirements

Before installing the plugin, ensure your system meets these requirements:

- **WordPress**: 5.8 or higher
- **WooCommerce**: 6.0 or higher  
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher
- Active WhatsApp number for receiving orders

## 🚀 Installation

### Method 1: WordPress Admin Dashboard (Recommended)

1. **Login to WordPress Admin**
   - Go to your WordPress admin dashboard
   - Navigate to `Plugins → Add New`

2. **Upload Plugin**
   - Click "Upload Plugin" button
   - Choose the `whatsapp-order-button.zip` file
   - Click "Install Now"

3. **Activate Plugin**
   - Click "Activate Plugin" after installation completes
   - You'll see a success message

### Method 2: FTP Upload

1. **Extract Files**
   - Extract the plugin zip file on your computer
   - You should see a folder named `whatsapp-order-button`

2. **Upload via FTP**
   - Connect to your website via FTP
   - Navigate to `/wp-content/plugins/`
   - Upload the `whatsapp-order-button` folder

3. **Activate Plugin**
   - Go to WordPress Admin → Plugins
   - Find "WhatsApp Order Button for WooCommerce"
   - Click "Activate"

## ⚙️ Initial Setup

### Step 1: Access Settings

1. Go to **WooCommerce → WhatsApp Order** in your admin menu
2. You'll see the plugin settings page with multiple tabs

### Step 2: Basic Configuration

#### General Settings Tab

1. **Enable Plugin**: Check the "Enable WhatsApp Order Button" checkbox
2. **WhatsApp Number**: Enter your WhatsApp number with country code
   - Format: `+1234567890` (include the + sign)
   - Example: `+1234567890` for US, `+************` for India
3. **Button Text**: Customize the button text (default: "Order on WhatsApp")
4. **Message Template**: Customize the auto-generated message using placeholders:
   ```
   Hi! I would like to order:
   
   Product: {product_name}
   Price: {price}
   Quantity: {quantity}
   {variations}
   
   Product Link: {product_url}
   ```

#### Available Placeholders:
- `{product_name}` - Product title
- `{price}` - Product price
- `{quantity}` - Selected quantity
- `{variations}` - Selected variations (size, color, etc.)
- `{product_url}` - Direct link to product page

### Step 3: Appearance Customization

#### Appearance Settings Tab

1. **Button Color**: Choose your brand color (default: WhatsApp green #25D366)
2. **Button Text Color**: Set text color (default: white #ffffff)
3. **Button Size**: Select from Small, Medium, or Large
4. **Button Position**: Choose where to display the button:
   - Before Add to Cart
   - After Add to Cart (recommended)
   - Replace Add to Cart

### Step 4: Display Options

#### Display Options Tab

1. **Show on Shop Page**: Enable to show buttons on product archive pages
2. **Show on Cart Page**: Enable WhatsApp ordering from cart
3. **Show on Checkout Page**: Offer WhatsApp as checkout alternative
4. **Floating Button**: Enable persistent floating WhatsApp button
5. **Size Guide**: Enable size guide links for clothing products

### Step 5: Advanced Settings

#### Advanced Settings Tab

1. **Device Behavior**: 
   - Auto: WhatsApp app on mobile, web on desktop (recommended)
   - App: Always try to open WhatsApp app
   - Web: Always use WhatsApp Web

2. **Analytics**: Enable click tracking for analytics
3. **Exclude Categories**: Select categories where button shouldn't appear

## 🎯 Usage Scenarios

### Scenario 1: Replace Traditional Checkout

**Perfect for**: Small clothing stores, boutiques, personal shopping

**Setup**:
1. Enable "Disable Add to Cart" in General settings
2. Set button position to "Replace Add to Cart"
3. Enable on all pages (shop, cart, checkout)

**Result**: Customers can only order via WhatsApp

### Scenario 2: Complement Existing Checkout

**Perfect for**: Established stores wanting to offer WhatsApp option

**Setup**:
1. Keep "Disable Add to Cart" unchecked
2. Set button position to "After Add to Cart"
3. Enable on product and cart pages

**Result**: Customers can choose between regular checkout or WhatsApp

### Scenario 3: Customer Support Focus

**Perfect for**: High-touch customer service businesses

**Setup**:
1. Enable floating button
2. Set custom message for general inquiries
3. Use per-product WhatsApp numbers for different departments

**Result**: Easy customer communication channel

## 🛠️ Advanced Configuration

### Per-Product Settings

1. **Edit any product** in WooCommerce
2. Scroll to **Product Data** section
3. Find **WhatsApp Options** tab
4. Configure:
   - Custom WhatsApp number for this product
   - Disable Add to Cart for this product only
   - Hide WhatsApp button for this product

### Per-Category Settings

1. Go to **Products → Categories**
2. **Edit any category**
3. Configure:
   - Custom WhatsApp number for category
   - Disable Add to Cart for entire category

### Custom Styling

Add custom CSS in **Appearance → Custom CSS** field:

```css
/* Example: Rounded button */
.whatsapp-order-button {
    border-radius: 50px;
    font-weight: bold;
}

/* Example: Pulse animation */
.whatsapp-floating-button {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
```

## 📱 Testing

### Test WhatsApp Integration

1. **Save your settings** with a valid WhatsApp number
2. **Visit a product page** on your website
3. **Click the WhatsApp button**
4. **Verify**:
   - WhatsApp opens correctly
   - Message contains product details
   - Number is correct

### Mobile Testing

1. **Open your website on mobile device**
2. **Click WhatsApp button**
3. **Verify**: WhatsApp app opens (not web version)

## 🔧 Troubleshooting

### Common Issues

**Button not appearing:**
- Check if plugin is enabled
- Verify WooCommerce is active
- Check if product/category is excluded

**WhatsApp not opening:**
- Verify WhatsApp number format (+countrycode + number)
- Test on different devices/browsers
- Check device behavior settings

**Message not formatted correctly:**
- Review message template
- Check placeholder syntax
- Test with different product types

### Debug Mode

Add this to your `wp-config.php` for debugging:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check `/wp-content/debug.log` for any plugin errors.

## 📞 Support

For additional support:

1. **Check FAQ** in readme.txt
2. **Review settings** - most issues are configuration-related
3. **Test with default theme** to rule out theme conflicts
4. **Contact support** with specific error messages and setup details

## 🔄 Updates

The plugin will notify you of updates through WordPress admin. Always:

1. **Backup your website** before updating
2. **Test on staging site** if possible
3. **Review changelog** for breaking changes

---

**Congratulations!** Your WhatsApp Order Button is now ready to boost your clothing store sales! 🎉
