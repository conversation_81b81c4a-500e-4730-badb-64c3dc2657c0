<?php
/**
 * Debug script for WhatsApp Order Button Analytics
 * 
 * This script helps debug analytics tracking issues.
 * Place this file in your WordPress root directory and access it via browser.
 * Remove after debugging is complete.
 */

// Load WordPress
require_once('wp-config.php');

echo "<h1>WhatsApp Order Button Analytics Debug</h1>";

// Check if plugin is active
if (!class_exists('WhatsApp_Order_Button')) {
    echo "<p style='color: red;'>❌ WhatsApp Order Button plugin is not active or not found.</p>";
    exit;
}

echo "<p style='color: green;'>✅ WhatsApp Order Button plugin is active.</p>";

// Get plugin instance
$plugin = WhatsApp_Order_Button::get_instance();

if (!$plugin) {
    echo "<p style='color: red;'>❌ Could not get plugin instance.</p>";
    exit;
}

echo "<p style='color: green;'>✅ Plugin instance retrieved successfully.</p>";

// Check if plugin is enabled
if (!$plugin->is_enabled()) {
    echo "<p style='color: red;'>❌ Plugin is not enabled in settings.</p>";
} else {
    echo "<p style='color: green;'>✅ Plugin is enabled.</p>";
}

// Check analytics setting
$analytics_enabled = $plugin->get_setting('enable_analytics', 'no');
echo "<p><strong>Analytics Enabled:</strong> " . ($analytics_enabled === 'yes' ? '<span style="color: green;">YES</span>' : '<span style="color: red;">NO</span>') . "</p>";

// Check WhatsApp number
$whatsapp_number = $plugin->get_setting('whatsapp_number', '');
echo "<p><strong>WhatsApp Number:</strong> " . (empty($whatsapp_number) ? '<span style="color: red;">NOT SET</span>' : '<span style="color: green;">' . esc_html($whatsapp_number) . '</span>') . "</p>";

// Check click tracking data
$clicks = get_option('whatsapp_order_clicks', array());
echo "<h2>Click Tracking Data</h2>";

if (empty($clicks)) {
    echo "<p style='color: orange;'>⚠️ No click tracking data found yet.</p>";
} else {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Date</th><th>Context</th><th>Clicks</th></tr>";
    
    foreach ($clicks as $date => $contexts) {
        foreach ($contexts as $context => $count) {
            echo "<tr>";
            echo "<td>" . esc_html($date) . "</td>";
            echo "<td>" . esc_html($context) . "</td>";
            echo "<td>" . esc_html($count) . "</td>";
            echo "</tr>";
        }
    }
    
    echo "</table>";
}

// Check if AJAX handlers are registered
echo "<h2>AJAX Handlers</h2>";

global $wp_filter;

$ajax_registered = false;
if (isset($wp_filter['wp_ajax_whatsapp_order_track']) && !empty($wp_filter['wp_ajax_whatsapp_order_track'])) {
    echo "<p style='color: green;'>✅ AJAX handler for logged-in users is registered.</p>";
    $ajax_registered = true;
} else {
    echo "<p style='color: red;'>❌ AJAX handler for logged-in users is NOT registered.</p>";
}

if (isset($wp_filter['wp_ajax_nopriv_whatsapp_order_track']) && !empty($wp_filter['wp_ajax_nopriv_whatsapp_order_track'])) {
    echo "<p style='color: green;'>✅ AJAX handler for non-logged-in users is registered.</p>";
    $ajax_registered = true;
} else {
    echo "<p style='color: red;'>❌ AJAX handler for non-logged-in users is NOT registered.</p>";
}

// Check if scripts are enqueued (this only works if we're on a page where they should be enqueued)
echo "<h2>Script Enqueuing</h2>";

if (wp_script_is('whatsapp-order-button', 'enqueued')) {
    echo "<p style='color: green;'>✅ WhatsApp Order Button JavaScript is enqueued.</p>";
} else {
    echo "<p style='color: orange;'>⚠️ WhatsApp Order Button JavaScript is not enqueued (this is normal if plugin is disabled or not on a WooCommerce page).</p>";
}

// Test AJAX endpoint
echo "<h2>AJAX Endpoint Test</h2>";
echo "<p><strong>AJAX URL:</strong> " . admin_url('admin-ajax.php') . "</p>";

// Create a test nonce
$test_nonce = wp_create_nonce('whatsapp_order_nonce');
echo "<p><strong>Test Nonce:</strong> " . $test_nonce . "</p>";

// JavaScript test
echo "<h2>JavaScript Test</h2>";
echo "<p>Open your browser's developer console and click the button below to test analytics tracking:</p>";

echo "<button id='test-analytics-btn' style='background: #25D366; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Analytics Tracking</button>";

echo "<script>
document.getElementById('test-analytics-btn').addEventListener('click', function() {
    console.log('Testing analytics tracking...');
    
    fetch('" . admin_url('admin-ajax.php') . "', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'whatsapp_order_track',
            product_id: 999,
            context: 'debug-test',
            nonce: '" . $test_nonce . "'
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Analytics test result:', data);
        if (data.success) {
            alert('✅ Analytics tracking test successful! Check console for details.');
        } else {
            alert('❌ Analytics tracking test failed: ' + (data.data || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Analytics test error:', error);
        alert('❌ Analytics tracking test failed with error. Check console for details.');
    });
});
</script>";

echo "<h2>Recommendations</h2>";
echo "<ul>";

if ($analytics_enabled !== 'yes') {
    echo "<li>Enable analytics in the plugin settings (WooCommerce → WhatsApp Order → General Settings)</li>";
}

if (empty($whatsapp_number)) {
    echo "<li>Set a WhatsApp number in the plugin settings</li>";
}

if (!$ajax_registered) {
    echo "<li>Check if the plugin's frontend class is properly initialized</li>";
}

echo "<li>Test the button on a WooCommerce product page, cart page, or checkout page</li>";
echo "<li>Check your browser's developer console for JavaScript errors</li>";
echo "<li>Check your WordPress error log for PHP errors</li>";
echo "</ul>";

echo "<p><em>Remember to delete this debug file after troubleshooting!</em></p>";
?>
