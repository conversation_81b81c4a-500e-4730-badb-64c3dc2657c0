/**
 * WhatsApp Order Button Admin JavaScript
 */

(function($) {
    'use strict';

    class WhatsAppOrderAdmin {
        constructor() {
            this.init();
        }

        init() {
            this.initTabs();
            this.initColorPickers();
            this.initPreview();
            this.initFormValidation();
            this.initDependencies();
            this.bindEvents();
        }

        initTabs() {
            // Tab functionality
            $('.nav-tab').on('click', (e) => {
                e.preventDefault();
                const target = $(e.currentTarget).attr('href');
                
                $('.nav-tab').removeClass('nav-tab-active');
                $(e.currentTarget).addClass('nav-tab-active');
                
                $('.tab-content').hide();
                $(target).show();
                
                // Save active tab to localStorage
                localStorage.setItem('whatsapp_order_active_tab', target);
            });

            // Restore active tab from localStorage
            const activeTab = localStorage.getItem('whatsapp_order_active_tab');
            if (activeTab && $(activeTab).length) {
                $(`a[href="${activeTab}"]`).click();
            }
        }

        initColorPickers() {
            // Initialize WordPress color pickers
            if ($.fn.wpColorPicker) {
                $('.color-picker').wpColorPicker({
                    change: () => {
                        this.updatePreview();
                    },
                    clear: () => {
                        this.updatePreview();
                    }
                });
            }
        }

        initPreview() {
            this.createPreviewSection();
            this.updatePreview();
        }

        createPreviewSection() {
            const previewHTML = `
                <div class="button-preview">
                    <h4>Button Preview</h4>
                    <div class="preview-container">
                        <button type="button" class="preview-button" id="whatsapp-preview-button">
                            <span class="whatsapp-icon">📱</span>
                            <span class="whatsapp-text">Order on WhatsApp</span>
                        </button>
                    </div>
                </div>
            `;
            
            $('#appearance .form-table').after(previewHTML);
        }

        updatePreview() {
            const $preview = $('#whatsapp-preview-button');
            if ($preview.length === 0) return;

            // Get current settings
            const buttonText = $('input[name="button_text"]').val() || 'Order on WhatsApp';
            const buttonColor = $('input[name="button_color"]').val() || '#25D366';
            const textColor = $('input[name="button_text_color"]').val() || '#ffffff';
            const buttonSize = $('select[name="button_size"]').val() || 'medium';

            // Update preview
            $preview.find('.whatsapp-text').text(buttonText);
            $preview.css({
                'background-color': buttonColor,
                'color': textColor
            });

            // Update size class
            $preview.removeClass('whatsapp-size-small whatsapp-size-medium whatsapp-size-large');
            $preview.addClass(`whatsapp-size-${buttonSize}`);
        }

        initFormValidation() {
            // WhatsApp number validation
            $('input[name="whatsapp_number"]').on('blur', this.validateWhatsAppNumber.bind(this));
            
            // Message template validation
            $('textarea[name="message_template"]').on('blur', this.validateMessageTemplate.bind(this));
        }

        validateWhatsAppNumber() {
            const $input = $('input[name="whatsapp_number"]');
            const number = $input.val().trim();
            
            if (number && !this.isValidWhatsAppNumber(number)) {
                this.showFieldError($input, 'Please enter a valid WhatsApp number with country code (e.g., +1234567890)');
                return false;
            } else {
                this.clearFieldError($input);
                return true;
            }
        }

        validateMessageTemplate() {
            const $textarea = $('textarea[name="message_template"]');
            const template = $textarea.val().trim();
            
            if (template && !template.includes('{product_name}')) {
                this.showFieldWarning($textarea, 'Consider including {product_name} placeholder in your message template');
            } else {
                this.clearFieldError($textarea);
            }
        }

        isValidWhatsAppNumber(number) {
            // Basic validation for WhatsApp number format
            const cleanNumber = number.replace(/[^0-9+]/g, '');
            return /^\+[1-9]\d{1,14}$/.test(cleanNumber);
        }

        showFieldError($field, message) {
            this.clearFieldError($field);
            $field.addClass('error');
            $field.after(`<div class="field-error" style="color: #dc3232; font-size: 12px; margin-top: 5px;">${message}</div>`);
        }

        showFieldWarning($field, message) {
            this.clearFieldError($field);
            $field.addClass('warning');
            $field.after(`<div class="field-warning" style="color: #f56e28; font-size: 12px; margin-top: 5px;">${message}</div>`);
        }

        clearFieldError($field) {
            $field.removeClass('error warning');
            $field.siblings('.field-error, .field-warning').remove();
        }

        initDependencies() {
            // Show/hide floating button position based on floating button setting
            this.toggleFloatingPosition();
            $('input[name="floating_button"]').on('change', this.toggleFloatingPosition.bind(this));

            // Show/hide size guide text based on size guide setting
            this.toggleSizeGuideText();
            $('input[name="show_size_guide"]').on('change', this.toggleSizeGuideText.bind(this));

            // Show/hide exclude options based on disable add to cart setting
            this.toggleExcludeOptions();
            $('input[name="disable_add_to_cart"]').on('change', this.toggleExcludeOptions.bind(this));
        }

        toggleFloatingPosition() {
            const isEnabled = $('input[name="floating_button"]').is(':checked');
            const $positionRow = $('select[name="floating_position"]').closest('tr');
            
            if (isEnabled) {
                $positionRow.show();
            } else {
                $positionRow.hide();
            }
        }

        toggleSizeGuideText() {
            const isEnabled = $('input[name="show_size_guide"]').is(':checked');
            const $textInput = $('input[name="size_guide_text"]').closest('tr');
            
            if (isEnabled) {
                $textInput.show();
            } else {
                $textInput.hide();
            }
        }

        toggleExcludeOptions() {
            const isGloballyDisabled = $('input[name="disable_add_to_cart"]').is(':checked');
            const $excludeRows = $('input[name="exclude_categories[]"], input[name="exclude_products[]"]').closest('tr');
            
            if (isGloballyDisabled) {
                $excludeRows.hide();
            } else {
                $excludeRows.show();
            }
        }

        bindEvents() {
            // Update preview when relevant fields change
            $('input[name="button_text"], select[name="button_size"]').on('input change', this.updatePreview.bind(this));
            
            // Test WhatsApp button
            $(document).on('click', '#test-whatsapp-button', this.testWhatsAppButton.bind(this));
            
            // Import/Export settings
            $(document).on('click', '#export-settings', this.exportSettings.bind(this));
            $(document).on('click', '#import-settings', this.importSettings.bind(this));
            
            // Reset to defaults
            $(document).on('click', '#reset-settings', this.resetSettings.bind(this));
            
            // Form submission validation
            $('form').on('submit', this.validateForm.bind(this));
        }

        testWhatsAppButton(e) {
            e.preventDefault();
            
            const whatsappNumber = $('input[name="whatsapp_number"]').val().trim();
            if (!whatsappNumber) {
                alert('Please enter a WhatsApp number first.');
                return;
            }

            if (!this.isValidWhatsAppNumber(whatsappNumber)) {
                alert('Please enter a valid WhatsApp number.');
                return;
            }

            const testMessage = 'This is a test message from WhatsApp Order Button plugin.';
            const whatsappUrl = this.generateWhatsAppUrl(whatsappNumber, testMessage);
            
            window.open(whatsappUrl, '_blank');
        }

        generateWhatsAppUrl(number, message) {
            const encodedMessage = encodeURIComponent(message);
            const cleanNumber = number.replace(/[^0-9]/g, '');
            return `https://web.whatsapp.com/send?phone=${cleanNumber}&text=${encodedMessage}`;
        }

        exportSettings() {
            const formData = new FormData($('form')[0]);
            const settings = {};
            
            for (let [key, value] of formData.entries()) {
                if (key !== 'submit' && key !== 'whatsapp_order_nonce') {
                    settings[key] = value;
                }
            }

            const dataStr = JSON.stringify(settings, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'whatsapp-order-settings.json';
            link.click();
        }

        importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const settings = JSON.parse(e.target.result);
                        this.applyImportedSettings(settings);
                        alert('Settings imported successfully!');
                    } catch (error) {
                        alert('Error importing settings: Invalid JSON file.');
                    }
                };
                reader.readAsText(file);
            };
            
            input.click();
        }

        applyImportedSettings(settings) {
            Object.keys(settings).forEach(key => {
                const $field = $(`[name="${key}"]`);
                if ($field.length) {
                    if ($field.is(':checkbox')) {
                        $field.prop('checked', settings[key] === 'yes');
                    } else {
                        $field.val(settings[key]);
                    }
                }
            });
            
            this.updatePreview();
            this.initDependencies();
        }

        resetSettings() {
            if (!confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
                return;
            }

            // Reset form to default values
            $('form')[0].reset();
            
            // Set specific default values
            $('input[name="enabled"]').prop('checked', true);
            $('input[name="button_text"]').val('Order on WhatsApp');
            $('input[name="button_color"]').val('#25D366');
            $('input[name="button_text_color"]').val('#ffffff');
            $('select[name="button_size"]').val('medium');
            $('select[name="button_position"]').val('after_add_to_cart');
            $('select[name="device_behavior"]').val('auto');
            
            this.updatePreview();
            this.initDependencies();
            
            alert('Settings reset to defaults.');
        }

        validateForm(e) {
            let isValid = true;
            
            // Validate WhatsApp number
            if (!this.validateWhatsAppNumber()) {
                isValid = false;
            }
            
            // Validate message template
            this.validateMessageTemplate();
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fix the errors before saving.');
            }
            
            return isValid;
        }
    }

    // Initialize when document is ready
    $(document).ready(() => {
        new WhatsAppOrderAdmin();
    });

})(jQuery);
