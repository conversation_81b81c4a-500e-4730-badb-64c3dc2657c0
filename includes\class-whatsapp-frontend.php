<?php
/**
 * WhatsApp Order Button Frontend Class
 *
 * @package WhatsApp_Order_Button
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WhatsApp Order Button Frontend Class
 */
class WhatsApp_Order_Button_Frontend {

    /**
     * Single instance of the class
     *
     * @var WhatsApp_Order_Button_Frontend
     */
    private static $instance = null;

    /**
     * Plugin instance
     *
     * @var WhatsApp_Order_Button
     */
    private $plugin;

    /**
     * Get single instance of the class
     *
     * @return WhatsApp_Order_Button_Frontend
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Don't call get_instance() here to avoid circular dependency
        // The plugin instance will be set after initialization
        $this->init_hooks();
    }

    /**
     * Set plugin instance (called after main plugin is fully initialized)
     */
    public function set_plugin_instance($plugin_instance) {
        $this->plugin = $plugin_instance;
    }

    /**
     * Get plugin instance safely
     */
    private function get_plugin() {
        if (!$this->plugin) {
            $this->plugin = WhatsApp_Order_Button::get_instance();
        }
        return $this->plugin;
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Always register basic hooks, check settings later when plugin is available
        add_action('woocommerce_single_product_summary', array($this, 'display_product_button'), 35);
        add_action('woocommerce_after_shop_loop_item', array($this, 'display_shop_button'), 15);

        // Cart page hooks - replace checkout button with WhatsApp button
        add_action('woocommerce_proceed_to_checkout', array($this, 'display_cart_button'), 5);
        add_action('init', array($this, 'maybe_replace_checkout_button'));

        add_action('woocommerce_checkout_before_customer_details', array($this, 'display_checkout_button'));
        add_action('init', array($this, 'maybe_disable_add_to_cart'));
        add_action('wp_footer', array($this, 'display_floating_button'));

        // AJAX handlers
        add_action('wp_ajax_whatsapp_order_track', array($this, 'track_click'));
        add_action('wp_ajax_nopriv_whatsapp_order_track', array($this, 'track_click'));

        // Add admin notice for analytics debugging (only for admins)
        if (is_admin() && current_user_can('manage_options')) {
            add_action('admin_notices', array($this, 'analytics_debug_notice'));
        }

        // Shortcodes
        add_shortcode('whatsapp_order_button', array($this, 'shortcode'));
        add_shortcode('whatsapp_cart_button', array($this, 'cart_shortcode'));
        add_shortcode('whatsapp_contact_button', array($this, 'contact_shortcode'));
        add_shortcode('whatsapp_floating_button', array($this, 'floating_shortcode'));
    }

    /**
     * Display WhatsApp button on product page
     */
    public function display_product_button() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled()) {
            return;
        }

        global $product;
        if (!$product || !$this->should_show_button($product)) {
            return;
        }

        $button_position = $plugin->get_setting('button_position', 'after_add_to_cart');

        if ('replace_add_to_cart' === $button_position) {
            remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
        }

        echo $this->generate_button($product);
    }

    /**
     * Display WhatsApp button on shop page
     */
    public function display_shop_button() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled() || 'yes' !== $plugin->get_setting('show_on_shop', 'no')) {
            return;
        }

        global $product;
        if (!$product || !$this->should_show_button($product)) {
            return;
        }

        echo $this->generate_button($product, 'shop');
    }

    /**
     * Maybe replace checkout button with WhatsApp button
     */
    public function maybe_replace_checkout_button() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled()) {
            return;
        }

        // Check if we should replace checkout button on cart page
        if ('yes' === $plugin->get_setting('replace_checkout_button', 'no')) {
            add_action('wp_head', array($this, 'hide_checkout_button_css'));
            add_action('woocommerce_proceed_to_checkout', array($this, 'display_whatsapp_checkout_replacement'), 20);
        }
    }

    /**
     * Hide the default checkout button with CSS
     */
    public function hide_checkout_button_css() {
        if (is_cart()) {
            echo '<style>
                .wc-proceed-to-checkout .checkout-button,
                .wc-proceed-to-checkout a.checkout-button {
                    display: none !important;
                }
            </style>';
        }
    }

    /**
     * Display WhatsApp button as checkout replacement
     */
    public function display_whatsapp_checkout_replacement() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled()) {
            return;
        }

        if (WC()->cart->is_empty()) {
            return;
        }

        $cart_items = WC()->cart->get_cart();
        $message = $this->generate_cart_message($cart_items);
        $whatsapp_number = $plugin->get_setting('whatsapp_number', '');

        if (empty($whatsapp_number)) {
            return;
        }

        $whatsapp_url = $plugin->generate_whatsapp_url($whatsapp_number, $message, null, 0);
        $button_text = $plugin->get_setting('button_text', __('Order on WhatsApp', 'whatsapp-order-button'));

        echo '<div class="whatsapp-checkout-replacement" style="text-align: center; margin-top: 20px;">';
        echo '<a href="' . esc_url($whatsapp_url) . '" class="button alt wc-forward whatsapp-order-button whatsapp-order-button-checkout" target="_blank" data-context="cart-checkout" data-whatsapp-number="' . esc_attr($whatsapp_number) . '" data-product-id="0" style="background-color: #25D366; color: white; padding: 15px 30px; font-size: 16px; border-radius: 5px; text-decoration: none; display: inline-block; width: 100%; max-width: 300px;">';
        echo '<span class="whatsapp-icon" style="margin-right: 10px;">📱</span>';
        echo esc_html($button_text);
        echo '</a>';
        echo '</div>';
    }

    /**
     * Display WhatsApp button on cart page (original method - now for additional placement)
     */
    public function display_cart_button() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled() || 'yes' !== $plugin->get_setting('show_on_cart', 'yes')) {
            return;
        }

        // Don't show if we're replacing the checkout button
        if ('yes' === $plugin->get_setting('replace_checkout_button', 'no')) {
            return;
        }

        if (WC()->cart->is_empty()) {
            return;
        }

        $cart_items = WC()->cart->get_cart();
        $message = $this->generate_cart_message($cart_items);
        $whatsapp_number = $plugin->get_setting('whatsapp_number', '');

        if (empty($whatsapp_number)) {
            return;
        }

        $whatsapp_url = $plugin->generate_whatsapp_url($whatsapp_number, $message, null, 0);

        echo '<div class="whatsapp-order-cart-button">';
        echo '<a href="' . esc_url($whatsapp_url) . '" class="button whatsapp-order-button" target="_blank" data-context="cart" data-whatsapp-number="' . esc_attr($whatsapp_number) . '" data-product-id="0">';
        echo '<span class="whatsapp-icon">📱</span>';
        echo esc_html($plugin->get_setting('button_text', __('Order on WhatsApp', 'whatsapp-order-button')));
        echo '</a>';
        echo '</div>';
    }

    /**
     * Display WhatsApp button on checkout page
     */
    public function display_checkout_button() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled() || 'yes' !== $plugin->get_setting('show_on_checkout', 'yes')) {
            return;
        }

        if (WC()->cart->is_empty()) {
            return;
        }

        echo '<div class="whatsapp-order-checkout-notice">';
        echo '<p>' . __('Prefer to order via WhatsApp?', 'whatsapp-order-button') . '</p>';
        $this->display_cart_button();
        echo '</div>';
    }

    /**
     * Display floating WhatsApp button
     */
    public function display_floating_button() {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled() || 'yes' !== $plugin->get_setting('floating_button', 'no')) {
            return;
        }

        $whatsapp_number = $plugin->get_setting('whatsapp_number', '');
        if (empty($whatsapp_number)) {
            return;
        }

        $position = $plugin->get_setting('floating_position', 'bottom-right');
        $message = __('Hi! I would like to know more about your products.', 'whatsapp-order-button');
        $whatsapp_url = $plugin->generate_whatsapp_url($whatsapp_number, $message, null, 0);

        echo '<div class="whatsapp-floating-button whatsapp-floating-' . esc_attr($position) . '">';
        echo '<a href="' . esc_url($whatsapp_url) . '" class="whatsapp-order-button" target="_blank" data-context="floating" data-whatsapp-number="' . esc_attr($whatsapp_number) . '" data-product-id="0">';
        echo '<span class="whatsapp-icon">💬</span>';
        echo '</a>';
        echo '</div>';
    }

    /**
     * Generate WhatsApp button HTML
     */
    private function generate_button($product, $context = 'product') {
        $plugin = $this->get_plugin();
        if (!$plugin) {
            return '';
        }

        $whatsapp_number = $plugin->get_whatsapp_number($product->get_id());

        if (empty($whatsapp_number)) {
            return '';
        }

        $button_text = $plugin->get_setting('button_text', __('Order on WhatsApp', 'whatsapp-order-button'));
        $button_color = $plugin->get_setting('button_color', '#25D366');
        $button_text_color = $plugin->get_setting('button_text_color', '#ffffff');
        $button_size = $plugin->get_setting('button_size', 'medium');

        $button_classes = array(
            'whatsapp-order-button',
            'whatsapp-order-' . $context,
            'whatsapp-size-' . $button_size
        );

        if ($product->is_type('variable')) {
            $button_classes[] = 'whatsapp-variable-product';
        }

        $button_style = sprintf(
            'background-color: %s; color: %s;',
            esc_attr($button_color),
            esc_attr($button_text_color)
        );

        $output = '<div class="whatsapp-order-button-wrapper">';

        // Size guide link (for clothing stores)
        if ('yes' === $plugin->get_setting('show_size_guide', 'no') && $this->has_size_attribute($product)) {
            $size_guide_text = $plugin->get_setting('size_guide_text', __('Size Guide', 'whatsapp-order-button'));
            $output .= '<a href="#" class="whatsapp-size-guide-link" data-product-id="' . $product->get_id() . '">' . esc_html($size_guide_text) . '</a>';
        }

        $output .= sprintf(
            '<button type="button" class="%s" style="%s" data-product-id="%d" data-whatsapp-number="%s" data-context="%s">',
            esc_attr(implode(' ', $button_classes)),
            $button_style,
            $product->get_id(),
            esc_attr($whatsapp_number),
            esc_attr($context)
        );
        
        $output .= '<span class="whatsapp-icon">📱</span>';
        $output .= '<span class="whatsapp-text">' . esc_html($button_text) . '</span>';
        $output .= '</button>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Generate message for cart items
     */
    private function generate_cart_message($cart_items) {
        $message = __('Hi! I would like to order the following items:', 'whatsapp-order-button') . "\n\n";
        
        foreach ($cart_items as $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];
            
            $message .= sprintf(
                "%s\n%s: %d\n%s: %s\n",
                $product->get_name(),
                __('Quantity', 'whatsapp-order-button'),
                $quantity,
                __('Price', 'whatsapp-order-button'),
                wc_price($product->get_price() * $quantity)
            );

            // Add variations
            if (!empty($cart_item['variation'])) {
                foreach ($cart_item['variation'] as $attribute => $value) {
                    $attribute_name = str_replace('attribute_', '', $attribute);
                    $message .= sprintf("%s: %s\n", ucfirst($attribute_name), $value);
                }
            }
            
            $message .= "\n";
        }

        $message .= sprintf(
            "%s: %s\n\n%s: %s",
            __('Total', 'whatsapp-order-button'),
            WC()->cart->get_total(),
            __('Cart Link', 'whatsapp-order-button'),
            wc_get_cart_url()
        );

        return $message;
    }

    /**
     * Check if button should be shown for product
     */
    private function should_show_button($product) {
        // Check if hidden for this product
        if ('yes' === get_post_meta($product->get_id(), '_hide_whatsapp_button', true)) {
            return false;
        }

        $plugin = $this->get_plugin();
        if (!$plugin) {
            return false;
        }

        // Check excluded categories
        $excluded_categories = $plugin->get_setting('exclude_categories', array());
        if (!empty($excluded_categories)) {
            $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'ids'));
            if (array_intersect($product_categories, $excluded_categories)) {
                return false;
            }
        }

        // Check excluded products
        $excluded_products = $plugin->get_setting('exclude_products', array());
        if (in_array($product->get_id(), $excluded_products)) {
            return false;
        }

        return true;
    }

    /**
     * Check if product has size attribute
     */
    private function has_size_attribute($product) {
        if ($product->is_type('variable')) {
            $attributes = $product->get_variation_attributes();
            return isset($attributes['size']) || isset($attributes['Size']) || isset($attributes['pa_size']);
        }
        return false;
    }

    /**
     * Maybe disable add to cart functionality
     */
    public function maybe_disable_add_to_cart() {
        $plugin = $this->get_plugin();
        if (!$plugin) {
            return;
        }

        if ('yes' === $plugin->get_setting('disable_add_to_cart', 'no')) {
            // Global disable
            remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
            remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10);
        } else {
            // Per-product disable
            add_filter('woocommerce_is_purchasable', array($this, 'maybe_disable_product_purchase'), 10, 2);
        }
    }

    /**
     * Maybe disable purchase for specific products
     */
    public function maybe_disable_product_purchase($purchasable, $product) {
        if ('yes' === get_post_meta($product->get_id(), '_disable_add_to_cart', true)) {
            return false;
        }

        // Check category-level disable
        $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'ids'));
        foreach ($product_categories as $category_id) {
            if ('yes' === get_term_meta($category_id, 'disable_add_to_cart', true)) {
                return false;
            }
        }

        return $purchasable;
    }

    /**
     * Track button clicks
     */
    public function track_click() {
        // Check if request method is POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            wp_send_json_error('Invalid request method');
            return;
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'whatsapp_order_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $product_id = intval(isset($_POST['product_id']) ? $_POST['product_id'] : 0);
        $context = sanitize_text_field(isset($_POST['context']) ? $_POST['context'] : 'unknown');

        // Log the tracking attempt for debugging
        error_log('WhatsApp Order Button: Tracking click - Product ID: ' . $product_id . ', Context: ' . $context);

        // Simple click tracking - you can extend this to save to database
        $clicks = get_option('whatsapp_order_clicks', array());
        $today = date('Y-m-d');

        if (!isset($clicks[$today])) {
            $clicks[$today] = array();
        }

        if (!isset($clicks[$today][$context])) {
            $clicks[$today][$context] = 0;
        }

        $clicks[$today][$context]++;

        // Update the option and check if it was successful
        $update_result = update_option('whatsapp_order_clicks', $clicks);

        if ($update_result === false) {
            error_log('WhatsApp Order Button: Failed to update click tracking data');
            wp_send_json_error('Failed to save tracking data');
            return;
        }

        // Send success response with tracking data
        wp_send_json_success(array(
            'product_id' => $product_id,
            'context' => $context,
            'total_clicks_today' => $clicks[$today][$context],
            'message' => 'Click tracked successfully'
        ));
    }

    /**
     * Enhanced shortcode handler
     */
    public function shortcode($atts) {
        $plugin = $this->get_plugin();
        if (!$plugin) {
            return '';
        }

        $atts = shortcode_atts(array(
            'product_id' => get_the_ID(),
            'text' => $plugin->get_setting('button_text', __('Order on WhatsApp', 'whatsapp-order-button')),
            'number' => '',
            'message' => '',
            'style' => 'default',
            'size' => 'medium',
            'color' => $plugin->get_setting('button_color', '#25D366'),
            'text_color' => $plugin->get_setting('button_text_color', '#ffffff'),
            'class' => '',
            'url' => ''
        ), $atts);

        // Handle different shortcode contexts
        if (!$atts['product_id'] || $atts['product_id'] === 'current') {
            $atts['product_id'] = get_the_ID();
        }

        $product = null;
        if ($atts['product_id']) {
            $product = wc_get_product($atts['product_id']);
        }

        return $this->generate_shortcode_button($atts, $product);
    }

    /**
     * Generate button for shortcode with custom parameters
     */
    private function generate_shortcode_button($atts, $product = null) {
        $plugin = $this->get_plugin();
        if (!$plugin) {
            return '';
        }

        // Determine WhatsApp number
        $whatsapp_number = $atts['number'];
        if (empty($whatsapp_number) && $product) {
            $whatsapp_number = $plugin->get_whatsapp_number($product->get_id());
        }
        if (empty($whatsapp_number)) {
            $whatsapp_number = $plugin->get_setting('whatsapp_number', '');
        }

        if (empty($whatsapp_number)) {
            return '';
        }

        // Generate message
        $message = $atts['message'];
        if (empty($message) && $product) {
            $message = $this->generate_product_message($product);
        }
        if (empty($message)) {
            $message = __('Hi! I would like to know more about your products.', 'whatsapp-order-button');
        }

        // Generate URL
        $url = '';
        if (!empty($atts['url'])) {
            // Custom URL provided in shortcode
            $url = $this->process_custom_shortcode_url($atts['url'], $product, $whatsapp_number, $message);
        } else {
            // Use standard URL generation
            $product_id = $product ? $product->get_id() : null;
            $url = $plugin->generate_whatsapp_url($whatsapp_number, $message, null, $product_id);
        }

        if (!$url || $url === '#error-custom-url-failed') {
            return '<span class="whatsapp-error">' . __('WhatsApp button configuration error', 'whatsapp-order-button') . '</span>';
        }

        // Build button classes
        $button_classes = array(
            'whatsapp-order-button',
            'whatsapp-shortcode-button',
            'whatsapp-size-' . esc_attr($atts['size']),
            'whatsapp-style-' . esc_attr($atts['style'])
        );

        if (!empty($atts['class'])) {
            $button_classes[] = esc_attr($atts['class']);
        }

        // Build button styles
        $button_style = sprintf(
            'background-color: %s; color: %s;',
            esc_attr($atts['color']),
            esc_attr($atts['text_color'])
        );

        // Generate button HTML
        $output = '<div class="whatsapp-shortcode-button-wrapper">';
        $output .= sprintf(
            '<a href="%s" class="%s" style="%s" target="_blank" data-context="shortcode" data-whatsapp-number="%s" data-product-id="%s">',
            esc_url($url),
            esc_attr(implode(' ', $button_classes)),
            $button_style,
            esc_attr($whatsapp_number),
            $product ? $product->get_id() : '0'
        );
        $output .= '<span class="whatsapp-icon">📱</span>';
        $output .= '<span class="whatsapp-text">' . esc_html($atts['text']) . '</span>';
        $output .= '</a>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Process custom URL from shortcode
     */
    private function process_custom_shortcode_url($url_template, $product, $whatsapp_number, $message) {
        $plugin = $this->get_plugin();
        if (!$plugin) {
            return '';
        }

        // Use the same placeholder system as the main custom URL feature
        return $plugin->generate_custom_url($whatsapp_number, $message, $product ? $product->get_id() : null);
    }

    /**
     * Generate product message for shortcode
     */
    private function generate_product_message($product) {
        $message = sprintf(
            __('Hi! I would like to order:\n\nProduct: %s\nPrice: %s\nProduct Link: %s', 'whatsapp-order-button'),
            $product->get_name(),
            wc_price($product->get_price()),
            $product->get_permalink()
        );

        return $message;
    }

    /**
     * Cart shortcode handler
     */
    public function cart_shortcode($atts) {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled()) {
            return '';
        }

        $atts = shortcode_atts(array(
            'text' => $plugin->get_setting('button_text', __('Order Cart on WhatsApp', 'whatsapp-order-button')),
            'number' => $plugin->get_setting('whatsapp_number', ''),
            'message' => '',
            'style' => 'default',
            'size' => 'medium',
            'color' => $plugin->get_setting('button_color', '#25D366'),
            'text_color' => $plugin->get_setting('button_text_color', '#ffffff'),
            'class' => ''
        ), $atts);

        if (WC()->cart->is_empty()) {
            return '<p class="whatsapp-cart-empty">' . __('Your cart is empty.', 'whatsapp-order-button') . '</p>';
        }

        // Generate cart message
        $cart_items = WC()->cart->get_cart();
        $message = $atts['message'];
        if (empty($message)) {
            $message = $this->generate_cart_message($cart_items);
        }

        $whatsapp_number = $atts['number'];
        if (empty($whatsapp_number)) {
            return '<span class="whatsapp-error">' . __('WhatsApp number not configured.', 'whatsapp-order-button') . '</span>';
        }

        $whatsapp_url = $plugin->generate_whatsapp_url($whatsapp_number, $message, null, 0);

        // Build button
        $button_classes = array(
            'whatsapp-order-button',
            'whatsapp-cart-shortcode-button',
            'whatsapp-size-' . esc_attr($atts['size']),
            'whatsapp-style-' . esc_attr($atts['style'])
        );

        if (!empty($atts['class'])) {
            $button_classes[] = esc_attr($atts['class']);
        }

        $button_style = sprintf(
            'background-color: %s; color: %s;',
            esc_attr($atts['color']),
            esc_attr($atts['text_color'])
        );

        $output = '<div class="whatsapp-cart-shortcode-wrapper">';
        $output .= sprintf(
            '<a href="%s" class="%s" style="%s" target="_blank" data-context="cart-shortcode" data-whatsapp-number="%s">',
            esc_url($whatsapp_url),
            esc_attr(implode(' ', $button_classes)),
            $button_style,
            esc_attr($whatsapp_number)
        );
        $output .= '<span class="whatsapp-icon">📱</span>';
        $output .= '<span class="whatsapp-text">' . esc_html($atts['text']) . '</span>';
        $output .= '</a>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Contact shortcode handler
     */
    public function contact_shortcode($atts) {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled()) {
            return '';
        }

        $atts = shortcode_atts(array(
            'text' => __('Contact us on WhatsApp', 'whatsapp-order-button'),
            'number' => $plugin->get_setting('whatsapp_number', ''),
            'message' => __('Hi! I would like to get in touch.', 'whatsapp-order-button'),
            'style' => 'default',
            'size' => 'medium',
            'color' => $plugin->get_setting('button_color', '#25D366'),
            'text_color' => $plugin->get_setting('button_text_color', '#ffffff'),
            'class' => ''
        ), $atts);

        $whatsapp_number = $atts['number'];
        if (empty($whatsapp_number)) {
            return '<span class="whatsapp-error">' . __('WhatsApp number not configured.', 'whatsapp-order-button') . '</span>';
        }

        $whatsapp_url = $plugin->generate_whatsapp_url($whatsapp_number, $atts['message'], null, 0);

        // Build button
        $button_classes = array(
            'whatsapp-order-button',
            'whatsapp-contact-shortcode-button',
            'whatsapp-size-' . esc_attr($atts['size']),
            'whatsapp-style-' . esc_attr($atts['style'])
        );

        if (!empty($atts['class'])) {
            $button_classes[] = esc_attr($atts['class']);
        }

        $button_style = sprintf(
            'background-color: %s; color: %s;',
            esc_attr($atts['color']),
            esc_attr($atts['text_color'])
        );

        $output = '<div class="whatsapp-contact-shortcode-wrapper">';
        $output .= sprintf(
            '<a href="%s" class="%s" style="%s" target="_blank" data-context="contact-shortcode" data-whatsapp-number="%s">',
            esc_url($whatsapp_url),
            esc_attr(implode(' ', $button_classes)),
            $button_style,
            esc_attr($whatsapp_number)
        );
        $output .= '<span class="whatsapp-icon">📱</span>';
        $output .= '<span class="whatsapp-text">' . esc_html($atts['text']) . '</span>';
        $output .= '</a>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Floating shortcode handler
     */
    public function floating_shortcode($atts) {
        $plugin = $this->get_plugin();
        if (!$plugin || !$plugin->is_enabled()) {
            return '';
        }

        $atts = shortcode_atts(array(
            'text' => '',
            'number' => $plugin->get_setting('whatsapp_number', ''),
            'message' => __('Hi! I would like to get in touch.', 'whatsapp-order-button'),
            'position' => 'bottom-right',
            'size' => 'medium',
            'color' => $plugin->get_setting('button_color', '#25D366'),
            'class' => ''
        ), $atts);

        $whatsapp_number = $atts['number'];
        if (empty($whatsapp_number)) {
            return '<span class="whatsapp-error">' . __('WhatsApp number not configured.', 'whatsapp-order-button') . '</span>';
        }

        $whatsapp_url = $plugin->generate_whatsapp_url($whatsapp_number, $atts['message'], null, 0);

        // Build floating button
        $button_classes = array(
            'whatsapp-floating-button',
            'whatsapp-floating-shortcode',
            'whatsapp-floating-' . esc_attr($atts['position']),
            'whatsapp-size-' . esc_attr($atts['size'])
        );

        if (!empty($atts['class'])) {
            $button_classes[] = esc_attr($atts['class']);
        }

        $button_style = sprintf(
            'background-color: %s; position: fixed; z-index: 9999;',
            esc_attr($atts['color'])
        );

        // Position styles
        switch ($atts['position']) {
            case 'bottom-left':
                $button_style .= ' bottom: 20px; left: 20px;';
                break;
            case 'top-right':
                $button_style .= ' top: 20px; right: 20px;';
                break;
            case 'top-left':
                $button_style .= ' top: 20px; left: 20px;';
                break;
            default: // bottom-right
                $button_style .= ' bottom: 20px; right: 20px;';
                break;
        }

        $output = sprintf(
            '<div class="%s" style="%s">',
            esc_attr(implode(' ', $button_classes)),
            $button_style
        );
        $output .= sprintf(
            '<a href="%s" target="_blank" data-context="floating-shortcode" data-whatsapp-number="%s" style="color: white; text-decoration: none; display: block; padding: 15px; border-radius: 50px;">',
            esc_url($whatsapp_url),
            esc_attr($whatsapp_number)
        );
        $output .= '<span class="whatsapp-icon">💬</span>';
        if (!empty($atts['text'])) {
            $output .= '<span class="whatsapp-text" style="margin-left: 8px;">' . esc_html($atts['text']) . '</span>';
        }
        $output .= '</a>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Show analytics debug notice for admins
     */
    public function analytics_debug_notice() {
        // Only show on WhatsApp Order settings page
        $screen = get_current_screen();
        if (!$screen || $screen->id !== 'woocommerce_page_whatsapp-order-settings') {
            return;
        }

        $plugin = $this->get_plugin();
        if (!$plugin) {
            return;
        }

        $analytics_enabled = $plugin->get_setting('enable_analytics', 'no');
        $clicks = get_option('whatsapp_order_clicks', array());

        if ($analytics_enabled === 'yes') {
            if (empty($clicks)) {
                echo '<div class="notice notice-info is-dismissible">';
                echo '<p><strong>WhatsApp Order Button:</strong> Analytics tracking is enabled but no clicks have been recorded yet. ';
                echo 'Test the button on your store to verify tracking is working. ';
                echo '<a href="' . esc_url(site_url('debug-analytics.php')) . '" target="_blank">Use debug tool</a> to troubleshoot.</p>';
                echo '</div>';
            } else {
                $total_clicks = 0;
                foreach ($clicks as $contexts) {
                    foreach ($contexts as $count) {
                        $total_clicks += $count;
                    }
                }
                echo '<div class="notice notice-success is-dismissible">';
                echo '<p><strong>WhatsApp Order Button:</strong> Analytics tracking is working! ';
                echo 'Total clicks recorded: <strong>' . $total_clicks . '</strong></p>';
                echo '</div>';
            }
        }
    }
}
