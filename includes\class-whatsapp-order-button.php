<?php
/**
 * Main WhatsApp Order Button Class
 *
 * @package WhatsApp_Order_Button
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main WhatsApp Order Button Class
 */
class WhatsApp_Order_Button {

    /**
     * Single instance of the class
     *
     * @var WhatsApp_Order_Button
     */
    private static $instance = null;

    /**
     * Plugin settings
     *
     * @var array
     */
    private $settings;

    /**
     * Get single instance of the class
     *
     * @return WhatsApp_Order_Button
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->settings = get_option('whatsapp_order_button_settings', array());
        $this->init_hooks();
        $this->includes();
    }

    /**
     * Include required files
     */
    private function includes() {
        $admin_file = WHATSAPP_ORDER_BUTTON_PLUGIN_DIR . 'includes/class-whatsapp-admin.php';
        $frontend_file = WHATSAPP_ORDER_BUTTON_PLUGIN_DIR . 'includes/class-whatsapp-frontend.php';

        // Check if files exist before including
        if (!file_exists($admin_file)) {
            add_action('admin_notices', function() use ($admin_file) {
                echo '<div class="notice notice-error"><p>WhatsApp Order Button: Admin class file not found at ' . esc_html($admin_file) . '</p></div>';
            });
            return;
        }

        if (!file_exists($frontend_file)) {
            add_action('admin_notices', function() use ($frontend_file) {
                echo '<div class="notice notice-error"><p>WhatsApp Order Button: Frontend class file not found at ' . esc_html($frontend_file) . '</p></div>';
            });
            return;
        }

        require_once $admin_file;
        require_once $frontend_file;

        // Initialize admin and frontend classes with error handling
        try {
            if (is_admin() && class_exists('WhatsApp_Order_Button_Admin')) {
                WhatsApp_Order_Button_Admin::get_instance();
            }

            if (class_exists('WhatsApp_Order_Button_Frontend')) {
                $frontend = WhatsApp_Order_Button_Frontend::get_instance();
                // Set the plugin instance to avoid circular dependency
                $frontend->set_plugin_instance($this);
            }
        } catch (Exception $e) {
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>WhatsApp Order Button class initialization error: ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . WHATSAPP_ORDER_BUTTON_PLUGIN_BASENAME, array($this, 'add_settings_link'));
        
        // HPOS compatibility
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        if (!$this->is_enabled()) {
            return;
        }

        wp_enqueue_style(
            'whatsapp-order-button',
            WHATSAPP_ORDER_BUTTON_PLUGIN_URL . 'public/css/whatsapp-order.css',
            array(),
            WHATSAPP_ORDER_BUTTON_VERSION
        );

        wp_enqueue_script(
            'whatsapp-order-button',
            WHATSAPP_ORDER_BUTTON_PLUGIN_URL . 'public/js/whatsapp-order.js',
            array('jquery'),
            WHATSAPP_ORDER_BUTTON_VERSION,
            true
        );

        // Localize script with settings
        wp_localize_script('whatsapp-order-button', 'whatsapp_order_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('whatsapp_order_nonce'),
            'device_behavior' => $this->get_setting('device_behavior', 'auto'),
            'enable_analytics' => $this->get_setting('enable_analytics', 'no'),
            'is_mobile' => wp_is_mobile(),
            'messages' => array(
                'select_options' => __('Please select product options before ordering.', 'whatsapp-order-button'),
                'quantity_required' => __('Please enter a valid quantity.', 'whatsapp-order-button')
            )
        ));
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if ('woocommerce_page_whatsapp-order-settings' !== $hook) {
            return;
        }

        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        
        wp_enqueue_style(
            'whatsapp-order-admin',
            WHATSAPP_ORDER_BUTTON_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            WHATSAPP_ORDER_BUTTON_VERSION
        );

        wp_enqueue_script(
            'whatsapp-order-admin',
            WHATSAPP_ORDER_BUTTON_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'wp-color-picker'),
            WHATSAPP_ORDER_BUTTON_VERSION,
            true
        );
    }

    /**
     * Add settings link to plugins page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=whatsapp-order-settings') . '">' . __('Settings', 'whatsapp-order-button') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Declare HPOS compatibility
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', WHATSAPP_ORDER_BUTTON_PLUGIN_FILE, true);
        }
    }

    /**
     * Check if plugin is enabled
     */
    public function is_enabled() {
        return 'yes' === $this->get_setting('enabled', 'yes');
    }

    /**
     * Get plugin setting
     */
    public function get_setting($key, $default = '') {
        return isset($this->settings[$key]) ? $this->settings[$key] : $default;
    }

    /**
     * Update plugin setting
     */
    public function update_setting($key, $value) {
        $this->settings[$key] = $value;
        update_option('whatsapp_order_button_settings', $this->settings);
    }

    /**
     * Get all settings
     */
    public function get_settings() {
        return $this->settings;
    }

    /**
     * Update all settings
     */
    public function update_settings($settings) {
        $this->settings = $settings;
        update_option('whatsapp_order_button_settings', $settings);
    }

    /**
     * Get WhatsApp number for product/category
     */
    public function get_whatsapp_number($product_id = null, $category_id = null) {
        // Check for product-specific number
        if ($product_id) {
            $product_number = get_post_meta($product_id, '_whatsapp_number', true);
            if (!empty($product_number)) {
                return $product_number;
            }
        }

        // Check for category-specific number
        if ($category_id) {
            $category_number = get_term_meta($category_id, 'whatsapp_number', true);
            if (!empty($category_number)) {
                return $category_number;
            }
        }

        // Return global number
        return $this->get_setting('whatsapp_number', '');
    }

    /**
     * Format WhatsApp number
     */
    public function format_whatsapp_number($number) {
        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $number);
        
        // Add country code if not present
        if (strlen($number) > 0 && substr($number, 0, 1) !== '+') {
            // You might want to add logic here to detect country and add appropriate code
            // For now, we'll assume the number is already in international format
        }
        
        return $number;
    }

    /**
     * Generate WhatsApp URL
     */
    public function generate_whatsapp_url($number, $message, $is_mobile = null, $product_id = null) {
        if (null === $is_mobile) {
            $is_mobile = wp_is_mobile();
        }

        // Check if custom URLs are enabled
        if ('yes' === $this->get_setting('enable_custom_urls', 'no')) {
            $custom_url = $this->generate_custom_url($number, $message, $product_id);
            if ($custom_url) {
                return $custom_url;
            }

            // Handle fallback behavior
            $fallback = $this->get_setting('custom_url_fallback', 'whatsapp');
            if ('hide' === $fallback) {
                return false;
            } elseif ('error' === $fallback) {
                return '#error-custom-url-failed';
            }
            // Continue to default WhatsApp URL for 'whatsapp' fallback
        }

        $formatted_number = $this->format_whatsapp_number($number);
        $encoded_message = urlencode($message);

        if ($is_mobile && 'web' !== $this->get_setting('device_behavior', 'auto')) {
            return "whatsapp://send?phone={$formatted_number}&text={$encoded_message}";
        } else {
            return "https://web.whatsapp.com/send?phone={$formatted_number}&text={$encoded_message}";
        }
    }

    /**
     * Generate custom URL using template
     */
    public function generate_custom_url($number, $message, $product_id = null) {
        // Get custom URL template - check product first, then global
        $template = '';

        if ($product_id) {
            $template = get_post_meta($product_id, '_custom_whatsapp_url', true);
        }

        if (empty($template)) {
            $template = $this->get_setting('custom_url_template', '');
        }

        if (empty($template)) {
            return false;
        }

        // Get product data if available
        $product = null;
        if ($product_id) {
            $product = wc_get_product($product_id);
        }

        // Prepare placeholders
        $placeholders = array(
            '{whatsapp_number}' => $this->format_whatsapp_number($number),
            '{encoded_message}' => urlencode($message),
            '{raw_message}' => $message,
            '{site_url}' => site_url(),
            '{site_name}' => get_bloginfo('name'),
            '{current_date}' => date('Y-m-d'),
            '{current_time}' => date('H:i:s'),
        );

        // Add product-specific placeholders
        if ($product) {
            $placeholders['{product_id}'] = $product->get_id();
            $placeholders['{product_name}'] = $product->get_name();
            $placeholders['{product_url}'] = $product->get_permalink();
            $placeholders['{price}'] = $product->get_price();
            $placeholders['{regular_price}'] = $product->get_regular_price();
            $placeholders['{sale_price}'] = $product->get_sale_price();
            $placeholders['{sku}'] = $product->get_sku();
            $placeholders['{product_type}'] = $product->get_type();
            $placeholders['{stock_status}'] = $product->get_stock_status();
            $placeholders['{categories}'] = implode(', ', wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'names')));
            $placeholders['{tags}'] = implode(', ', wp_get_post_terms($product->get_id(), 'product_tag', array('fields' => 'names')));
        }

        // Apply filters to allow custom placeholders
        $placeholders = apply_filters('whatsapp_order_button_url_placeholders', $placeholders, $product_id, $number, $message);

        // Replace placeholders in template
        $custom_url = str_replace(array_keys($placeholders), array_values($placeholders), $template);

        // Validate the generated URL
        if (!filter_var($custom_url, FILTER_VALIDATE_URL)) {
            error_log('WhatsApp Order Button: Invalid custom URL generated: ' . $custom_url);
            return false;
        }

        return $custom_url;
    }

    /**
     * Get custom URL for product
     */
    public function get_custom_url($product_id, $number, $message) {
        return $this->generate_custom_url($number, $message, $product_id);
    }
}
