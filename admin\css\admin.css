/**
 * WhatsApp Order Button Admin Styles
 */

/* Settings Page */
.whatsapp-order-settings {
    max-width: 1200px;
}

/* Tab Navigation */
.nav-tab-wrapper {
    margin-bottom: 20px;
    border-bottom: 1px solid #ccd0d4;
}

.nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    color: #555;
    text-decoration: none;
    padding: 10px 15px;
    margin-right: 5px;
    border-bottom: none;
    position: relative;
    top: 1px;
}

.nav-tab:hover {
    background: #fff;
    color: #444;
}

.nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #000;
}

/* Tab Content */
.tab-content {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-top: none;
}

.tab-content h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* Form Styling */
.form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
}

.form-table td {
    padding: 15px 10px;
    vertical-align: top;
}

.form-table input[type="text"],
.form-table input[type="number"],
.form-table textarea,
.form-table select {
    width: 100%;
    max-width: 400px;
}

.form-table textarea {
    height: 120px;
    font-family: monospace;
}

.form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Color Picker */
.wp-picker-container {
    display: inline-block;
}

.wp-color-result {
    height: 30px;
    width: 60px;
}

/* Checkbox Groups */
.checkbox-group {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f9f9f9;
}

.checkbox-group label {
    display: block;
    margin-bottom: 5px;
    padding: 2px 0;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Product Meta Box */
.whatsapp-product-options {
    padding: 12px;
}

.whatsapp-product-options .form-field {
    margin-bottom: 15px;
}

.whatsapp-product-options label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.whatsapp-product-options input[type="text"] {
    width: 100%;
    max-width: 300px;
}

.whatsapp-product-options input[type="checkbox"] {
    margin-right: 8px;
}

/* Category Fields */
.form-field input[type="text"] {
    width: 300px;
}

/* Settings Sections */
.settings-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #e1e1e1;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.settings-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* Preview Section */
.button-preview {
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin-top: 15px;
}

.button-preview h4 {
    margin-top: 0;
    margin-bottom: 15px;
}

.preview-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: #25D366;
    color: #ffffff;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);
}

.preview-button:hover {
    background-color: #128C7E;
    color: #ffffff;
}

/* Help Text */
.help-text {
    background: #e7f3ff;
    border-left: 4px solid #0073aa;
    padding: 12px;
    margin: 15px 0;
}

.help-text h4 {
    margin-top: 0;
    color: #0073aa;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-enabled {
    background-color: #46b450;
}

.status-disabled {
    background-color: #dc3232;
}

/* Analytics Section */
.analytics-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #25D366;
    display: block;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .nav-tab {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }
    
    .analytics-stats {
        grid-template-columns: 1fr;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #25D366;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.notice-whatsapp {
    border-left-color: #25D366;
}

.notice-whatsapp .notice-title {
    color: #25D366;
    font-weight: 600;
}

/* Code Editor */
.code-editor {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    background: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
