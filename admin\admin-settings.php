<?php
/**
 * Admin Settings Page
 *
 * @package WhatsApp_Order_Button
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('WhatsApp Order Button Settings', 'whatsapp-order-button'); ?></h1>
    
    <form method="post" action="">
        <?php wp_nonce_field('whatsapp_order_settings', 'whatsapp_order_nonce'); ?>
        
        <nav class="nav-tab-wrapper">
            <a href="#general" class="nav-tab nav-tab-active"><?php _e('General', 'whatsapp-order-button'); ?></a>
            <a href="#appearance" class="nav-tab"><?php _e('Appearance', 'whatsapp-order-button'); ?></a>
            <a href="#display" class="nav-tab"><?php _e('Display Options', 'whatsapp-order-button'); ?></a>
            <a href="#shortcodes" class="nav-tab"><?php _e('Shortcodes & URLs', 'whatsapp-order-button'); ?></a>
            <a href="#advanced" class="nav-tab"><?php _e('Advanced', 'whatsapp-order-button'); ?></a>
        </nav>

        <!-- General Settings -->
        <div id="general" class="tab-content">
            <h2><?php _e('General Settings', 'whatsapp-order-button'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Enable Plugin', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enabled" value="yes" <?php checked(isset($settings['enabled']) ? $settings['enabled'] : 'yes', 'yes'); ?> />
                            <?php _e('Enable WhatsApp Order Button', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('WhatsApp Number', 'whatsapp-order-button'); ?></th>
                    <td>
                        <input type="text" name="whatsapp_number" value="<?php echo esc_attr(isset($settings['whatsapp_number']) ? $settings['whatsapp_number'] : ''); ?>" class="regular-text" placeholder="+1234567890" />
                        <p class="description"><?php _e('Enter your WhatsApp number with country code (e.g., +1234567890)', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Button Text', 'whatsapp-order-button'); ?></th>
                    <td>
                        <input type="text" name="button_text" value="<?php echo esc_attr(isset($settings['button_text']) ? $settings['button_text'] : __('Order on WhatsApp', 'whatsapp-order-button')); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Message Template', 'whatsapp-order-button'); ?></th>
                    <td>
                        <textarea name="message_template" rows="6" cols="50" class="large-text"><?php echo esc_textarea(isset($settings['message_template']) ? $settings['message_template'] : ''); ?></textarea>
                        <p class="description">
                            <?php _e('Available placeholders:', 'whatsapp-order-button'); ?>
                            <code>{product_name}</code>, <code>{price}</code>, <code>{quantity}</code>, <code>{variations}</code>, <code>{product_url}</code>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Disable Add to Cart', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="disable_add_to_cart" value="yes" <?php checked(isset($settings['disable_add_to_cart']) ? $settings['disable_add_to_cart'] : 'no', 'yes'); ?> />
                            <?php _e('Disable Add to Cart globally (force WhatsApp ordering)', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Appearance Settings -->
        <div id="appearance" class="tab-content" style="display: none;">
            <h2><?php _e('Appearance Settings', 'whatsapp-order-button'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Button Color', 'whatsapp-order-button'); ?></th>
                    <td>
                        <input type="text" name="button_color" value="<?php echo esc_attr(isset($settings['button_color']) ? $settings['button_color'] : '#25D366'); ?>" class="color-picker" />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Button Text Color', 'whatsapp-order-button'); ?></th>
                    <td>
                        <input type="text" name="button_text_color" value="<?php echo esc_attr(isset($settings['button_text_color']) ? $settings['button_text_color'] : '#ffffff'); ?>" class="color-picker" />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Button Size', 'whatsapp-order-button'); ?></th>
                    <td>
                        <select name="button_size">
                            <option value="small" <?php selected(isset($settings['button_size']) ? $settings['button_size'] : 'medium', 'small'); ?>><?php _e('Small', 'whatsapp-order-button'); ?></option>
                            <option value="medium" <?php selected(isset($settings['button_size']) ? $settings['button_size'] : 'medium', 'medium'); ?>><?php _e('Medium', 'whatsapp-order-button'); ?></option>
                            <option value="large" <?php selected(isset($settings['button_size']) ? $settings['button_size'] : 'medium', 'large'); ?>><?php _e('Large', 'whatsapp-order-button'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Button Position', 'whatsapp-order-button'); ?></th>
                    <td>
                        <select name="button_position">
                            <option value="before_add_to_cart" <?php selected(isset($settings['button_position']) ? $settings['button_position'] : 'after_add_to_cart', 'before_add_to_cart'); ?>><?php _e('Before Add to Cart', 'whatsapp-order-button'); ?></option>
                            <option value="after_add_to_cart" <?php selected(isset($settings['button_position']) ? $settings['button_position'] : 'after_add_to_cart', 'after_add_to_cart'); ?>><?php _e('After Add to Cart', 'whatsapp-order-button'); ?></option>
                            <option value="replace_add_to_cart" <?php selected(isset($settings['button_position']) ? $settings['button_position'] : 'after_add_to_cart', 'replace_add_to_cart'); ?>><?php _e('Replace Add to Cart', 'whatsapp-order-button'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Custom CSS', 'whatsapp-order-button'); ?></th>
                    <td>
                        <textarea name="custom_css" rows="6" cols="50" class="large-text code"><?php echo esc_textarea(isset($settings['custom_css']) ? $settings['custom_css'] : ''); ?></textarea>
                        <p class="description"><?php _e('Add custom CSS to style the WhatsApp button', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Display Options -->
        <div id="display" class="tab-content" style="display: none;">
            <h2><?php _e('Display Options', 'whatsapp-order-button'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Show on Shop Page', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="show_on_shop" value="yes" <?php checked(isset($settings['show_on_shop']) ? $settings['show_on_shop'] : 'no', 'yes'); ?> />
                            <?php _e('Show WhatsApp button on shop/archive pages', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Show on Cart Page', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="show_on_cart" value="yes" <?php checked(isset($settings['show_on_cart']) ? $settings['show_on_cart'] : 'yes', 'yes'); ?> />
                            <?php _e('Show WhatsApp button on cart page', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Replace Checkout Button', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="replace_checkout_button" value="yes" <?php checked(isset($settings['replace_checkout_button']) ? $settings['replace_checkout_button'] : 'no', 'yes'); ?> />
                            <?php _e('Replace the checkout button with WhatsApp button on cart page', 'whatsapp-order-button'); ?>
                        </label>
                        <p class="description"><?php _e('When enabled, the default "Proceed to Checkout" button will be hidden and replaced with the WhatsApp order button.', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Show on Checkout Page', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="show_on_checkout" value="yes" <?php checked(isset($settings['show_on_checkout']) ? $settings['show_on_checkout'] : 'yes', 'yes'); ?> />
                            <?php _e('Show WhatsApp button on checkout page', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Floating Button', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="floating_button" value="yes" <?php checked(isset($settings['floating_button']) ? $settings['floating_button'] : 'no', 'yes'); ?> />
                            <?php _e('Show floating WhatsApp button', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Floating Button Position', 'whatsapp-order-button'); ?></th>
                    <td>
                        <select name="floating_position">
                            <option value="bottom-right" <?php selected(isset($settings['floating_position']) ? $settings['floating_position'] : 'bottom-right', 'bottom-right'); ?>><?php _e('Bottom Right', 'whatsapp-order-button'); ?></option>
                            <option value="bottom-left" <?php selected(isset($settings['floating_position']) ? $settings['floating_position'] : 'bottom-right', 'bottom-left'); ?>><?php _e('Bottom Left', 'whatsapp-order-button'); ?></option>
                            <option value="top-right" <?php selected(isset($settings['floating_position']) ? $settings['floating_position'] : 'bottom-right', 'top-right'); ?>><?php _e('Top Right', 'whatsapp-order-button'); ?></option>
                            <option value="top-left" <?php selected(isset($settings['floating_position']) ? $settings['floating_position'] : 'bottom-right', 'top-left'); ?>><?php _e('Top Left', 'whatsapp-order-button'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Size Guide', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="show_size_guide" value="yes" <?php checked(isset($settings['show_size_guide']) ? $settings['show_size_guide'] : 'no', 'yes'); ?> />
                            <?php _e('Show size guide link for clothing products', 'whatsapp-order-button'); ?>
                        </label>
                        <br><br>
                        <input type="text" name="size_guide_text" value="<?php echo esc_attr(isset($settings['size_guide_text']) ? $settings['size_guide_text'] : __('Size Guide', 'whatsapp-order-button')); ?>" class="regular-text" placeholder="<?php _e('Size Guide', 'whatsapp-order-button'); ?>" />
                        <p class="description"><?php _e('Text for the size guide link', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Shortcodes & URLs Settings -->
        <div id="shortcodes" class="tab-content" style="display: none;">
            <h2><?php _e('Shortcodes & URL Customization', 'whatsapp-order-button'); ?></h2>

            <!-- URL Customization Section -->
            <h3><?php _e('Custom URL Templates', 'whatsapp-order-button'); ?></h3>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Enable Custom URLs', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_custom_urls" value="yes" <?php checked(isset($settings['enable_custom_urls']) ? $settings['enable_custom_urls'] : 'no', 'yes'); ?> />
                            <?php _e('Enable custom URL templates for WhatsApp buttons', 'whatsapp-order-button'); ?>
                        </label>
                        <p class="description"><?php _e('When enabled, you can create custom URL patterns instead of standard WhatsApp URLs.', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Custom URL Template', 'whatsapp-order-button'); ?></th>
                    <td>
                        <textarea name="custom_url_template" rows="4" cols="70" class="large-text" placeholder="https://wa.me/{whatsapp_number}?text={encoded_message}"><?php echo esc_textarea(isset($settings['custom_url_template']) ? $settings['custom_url_template'] : ''); ?></textarea>
                        <p class="description">
                            <?php _e('Available placeholders:', 'whatsapp-order-button'); ?><br>
                            <code>{whatsapp_number}</code> - <?php _e('WhatsApp number', 'whatsapp-order-button'); ?><br>
                            <code>{encoded_message}</code> - <?php _e('URL encoded message', 'whatsapp-order-button'); ?><br>
                            <code>{product_id}</code> - <?php _e('Product ID', 'whatsapp-order-button'); ?><br>
                            <code>{product_name}</code> - <?php _e('Product name', 'whatsapp-order-button'); ?><br>
                            <code>{product_url}</code> - <?php _e('Product URL', 'whatsapp-order-button'); ?><br>
                            <code>{price}</code> - <?php _e('Product price', 'whatsapp-order-button'); ?><br>
                            <code>{site_url}</code> - <?php _e('Site URL', 'whatsapp-order-button'); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Fallback Behavior', 'whatsapp-order-button'); ?></th>
                    <td>
                        <select name="custom_url_fallback">
                            <option value="whatsapp" <?php selected(isset($settings['custom_url_fallback']) ? $settings['custom_url_fallback'] : 'whatsapp', 'whatsapp'); ?>><?php _e('Use WhatsApp URL if custom fails', 'whatsapp-order-button'); ?></option>
                            <option value="hide" <?php selected(isset($settings['custom_url_fallback']) ? $settings['custom_url_fallback'] : 'whatsapp', 'hide'); ?>><?php _e('Hide button if custom URL fails', 'whatsapp-order-button'); ?></option>
                            <option value="error" <?php selected(isset($settings['custom_url_fallback']) ? $settings['custom_url_fallback'] : 'whatsapp', 'error'); ?>><?php _e('Show error message', 'whatsapp-order-button'); ?></option>
                        </select>
                        <p class="description"><?php _e('What to do when custom URL template fails to generate a valid URL.', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
            </table>

            <!-- Shortcode Generator Section -->
            <h3><?php _e('Shortcode Generator', 'whatsapp-order-button'); ?></h3>
            <div class="shortcode-generator-wrapper">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Shortcode Type', 'whatsapp-order-button'); ?></th>
                        <td>
                            <select id="shortcode-type">
                                <option value="whatsapp_order_button"><?php _e('Product Order Button', 'whatsapp-order-button'); ?></option>
                                <option value="whatsapp_cart_button"><?php _e('Cart Button', 'whatsapp-order-button'); ?></option>
                                <option value="whatsapp_contact_button"><?php _e('Contact Button', 'whatsapp-order-button'); ?></option>
                                <option value="whatsapp_floating_button"><?php _e('Floating Button', 'whatsapp-order-button'); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Product ID', 'whatsapp-order-button'); ?></th>
                        <td>
                            <input type="number" id="shortcode-product-id" placeholder="<?php _e('Leave empty for current product', 'whatsapp-order-button'); ?>" />
                            <p class="description"><?php _e('Specific product ID (optional)', 'whatsapp-order-button'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Button Text', 'whatsapp-order-button'); ?></th>
                        <td>
                            <input type="text" id="shortcode-text" placeholder="<?php _e('Custom button text', 'whatsapp-order-button'); ?>" />
                            <p class="description"><?php _e('Custom text for the button (optional)', 'whatsapp-order-button'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('WhatsApp Number', 'whatsapp-order-button'); ?></th>
                        <td>
                            <input type="text" id="shortcode-number" placeholder="+1234567890" />
                            <p class="description"><?php _e('Override default WhatsApp number (optional)', 'whatsapp-order-button'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Custom Message', 'whatsapp-order-button'); ?></th>
                        <td>
                            <textarea id="shortcode-message" rows="3" cols="50" placeholder="<?php _e('Custom WhatsApp message', 'whatsapp-order-button'); ?>"></textarea>
                            <p class="description"><?php _e('Custom message template (optional)', 'whatsapp-order-button'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Button Style', 'whatsapp-order-button'); ?></th>
                        <td>
                            <select id="shortcode-style">
                                <option value=""><?php _e('Default', 'whatsapp-order-button'); ?></option>
                                <option value="inline"><?php _e('Inline', 'whatsapp-order-button'); ?></option>
                                <option value="floating"><?php _e('Floating', 'whatsapp-order-button'); ?></option>
                                <option value="minimal"><?php _e('Minimal', 'whatsapp-order-button'); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Button Size', 'whatsapp-order-button'); ?></th>
                        <td>
                            <select id="shortcode-size">
                                <option value=""><?php _e('Default', 'whatsapp-order-button'); ?></option>
                                <option value="small"><?php _e('Small', 'whatsapp-order-button'); ?></option>
                                <option value="medium"><?php _e('Medium', 'whatsapp-order-button'); ?></option>
                                <option value="large"><?php _e('Large', 'whatsapp-order-button'); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Button Color', 'whatsapp-order-button'); ?></th>
                        <td>
                            <input type="text" id="shortcode-color" class="color-picker" />
                            <p class="description"><?php _e('Custom button color (optional)', 'whatsapp-order-button'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('CSS Class', 'whatsapp-order-button'); ?></th>
                        <td>
                            <input type="text" id="shortcode-class" placeholder="my-custom-class" />
                            <p class="description"><?php _e('Additional CSS classes (optional)', 'whatsapp-order-button'); ?></p>
                        </td>
                    </tr>
                </table>

                <div class="shortcode-output-section">
                    <h4><?php _e('Generated Shortcode', 'whatsapp-order-button'); ?></h4>
                    <div class="shortcode-output-wrapper">
                        <textarea id="generated-shortcode" readonly rows="3" cols="70">[whatsapp_order_button]</textarea>
                        <button type="button" id="copy-shortcode" class="button button-secondary"><?php _e('Copy Shortcode', 'whatsapp-order-button'); ?></button>
                    </div>
                    <p class="description"><?php _e('Copy this shortcode and paste it into any post, page, or widget.', 'whatsapp-order-button'); ?></p>
                </div>

                <div class="shortcode-preview-section">
                    <h4><?php _e('Live Preview', 'whatsapp-order-button'); ?></h4>
                    <div id="shortcode-preview" class="shortcode-preview-area">
                        <!-- Preview will be generated here -->
                    </div>
                </div>
            </div>

            <!-- Available Shortcodes Reference -->
            <h3><?php _e('Available Shortcodes Reference', 'whatsapp-order-button'); ?></h3>
            <div class="shortcode-reference">
                <h4><code>[whatsapp_order_button]</code></h4>
                <p><?php _e('Display WhatsApp order button for products.', 'whatsapp-order-button'); ?></p>
                <p><strong><?php _e('Parameters:', 'whatsapp-order-button'); ?></strong> product_id, text, number, message, style, size, color, class</p>

                <h4><code>[whatsapp_cart_button]</code></h4>
                <p><?php _e('Display WhatsApp button for cart contents.', 'whatsapp-order-button'); ?></p>
                <p><strong><?php _e('Parameters:', 'whatsapp-order-button'); ?></strong> text, number, message, style, size, color, class</p>

                <h4><code>[whatsapp_contact_button]</code></h4>
                <p><?php _e('Display general WhatsApp contact button.', 'whatsapp-order-button'); ?></p>
                <p><strong><?php _e('Parameters:', 'whatsapp-order-button'); ?></strong> text, number, message, style, size, color, class</p>

                <h4><code>[whatsapp_floating_button]</code></h4>
                <p><?php _e('Display floating WhatsApp button.', 'whatsapp-order-button'); ?></p>
                <p><strong><?php _e('Parameters:', 'whatsapp-order-button'); ?></strong> text, number, message, position, size, color, class</p>
            </div>
        </div>

        <!-- Advanced Settings -->
        <div id="advanced" class="tab-content" style="display: none;">
            <h2><?php _e('Advanced Settings', 'whatsapp-order-button'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Device Behavior', 'whatsapp-order-button'); ?></th>
                    <td>
                        <select name="device_behavior">
                            <option value="auto" <?php selected(isset($settings['device_behavior']) ? $settings['device_behavior'] : 'auto', 'auto'); ?>><?php _e('Auto (App on mobile, Web on desktop)', 'whatsapp-order-button'); ?></option>
                            <option value="app" <?php selected(isset($settings['device_behavior']) ? $settings['device_behavior'] : 'auto', 'app'); ?>><?php _e('Always use WhatsApp App', 'whatsapp-order-button'); ?></option>
                            <option value="web" <?php selected(isset($settings['device_behavior']) ? $settings['device_behavior'] : 'auto', 'web'); ?>><?php _e('Always use WhatsApp Web', 'whatsapp-order-button'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Enable Analytics', 'whatsapp-order-button'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_analytics" value="yes" <?php checked(isset($settings['enable_analytics']) ? $settings['enable_analytics'] : 'no', 'yes'); ?> />
                            <?php _e('Track button clicks for analytics', 'whatsapp-order-button'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Exclude Categories', 'whatsapp-order-button'); ?></th>
                    <td>
                        <?php
                        $categories = get_terms(array(
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                        ));
                        $excluded_categories = isset($settings['exclude_categories']) ? $settings['exclude_categories'] : array();
                        
                        if (!empty($categories)) {
                            foreach ($categories as $category) {
                                $checked = in_array($category->term_id, $excluded_categories) ? 'checked' : '';
                                echo '<label><input type="checkbox" name="exclude_categories[]" value="' . $category->term_id . '" ' . $checked . ' /> ' . $category->name . '</label><br>';
                            }
                        }
                        ?>
                        <p class="description"><?php _e('Select categories where WhatsApp button should not appear', 'whatsapp-order-button'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <?php submit_button(); ?>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Tab functionality
    $('.nav-tab').click(function(e) {
        e.preventDefault();
        var target = $(this).attr('href');

        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        $('.tab-content').hide();
        $(target).show();
    });

    // Color picker
    $('.color-picker').wpColorPicker();

    // Shortcode Generator Functionality
    function generateShortcode() {
        var type = $('#shortcode-type').val();
        var productId = $('#shortcode-product-id').val();
        var text = $('#shortcode-text').val();
        var number = $('#shortcode-number').val();
        var message = $('#shortcode-message').val();
        var style = $('#shortcode-style').val();
        var size = $('#shortcode-size').val();
        var color = $('#shortcode-color').val();
        var cssClass = $('#shortcode-class').val();

        var shortcode = '[' + type;
        var params = [];

        if (productId) params.push('product_id="' + productId + '"');
        if (text) params.push('text="' + text + '"');
        if (number) params.push('number="' + number + '"');
        if (message) params.push('message="' + message + '"');
        if (style) params.push('style="' + style + '"');
        if (size) params.push('size="' + size + '"');
        if (color) params.push('color="' + color + '"');
        if (cssClass) params.push('class="' + cssClass + '"');

        if (params.length > 0) {
            shortcode += ' ' + params.join(' ');
        }

        shortcode += ']';
        $('#generated-shortcode').val(shortcode);
        updatePreview();
    }

    function updatePreview() {
        var shortcode = $('#generated-shortcode').val();
        var type = $('#shortcode-type').val();
        var text = $('#shortcode-text').val() || '<?php echo esc_js(__('Order on WhatsApp', 'whatsapp-order-button')); ?>';
        var color = $('#shortcode-color').val() || '#25D366';
        var size = $('#shortcode-size').val() || 'medium';
        var style = $('#shortcode-style').val() || 'default';

        var sizeClass = 'whatsapp-size-' + size;
        var styleClass = 'whatsapp-style-' + style;

        var previewHtml = '<button type="button" class="whatsapp-order-button ' + sizeClass + ' ' + styleClass + '" style="background-color: ' + color + '; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">';
        previewHtml += '<span class="whatsapp-icon" style="margin-right: 8px;">📱</span>';
        previewHtml += '<span class="whatsapp-text">' + text + '</span>';
        previewHtml += '</button>';

        $('#shortcode-preview').html(previewHtml);
    }

    // Bind events for shortcode generator
    $('#shortcode-type, #shortcode-product-id, #shortcode-text, #shortcode-number, #shortcode-message, #shortcode-style, #shortcode-size, #shortcode-class').on('input change', generateShortcode);
    $('#shortcode-color').on('wpcolorpickerchange', generateShortcode);

    // Copy shortcode functionality
    $('#copy-shortcode').click(function() {
        var shortcodeText = $('#generated-shortcode');
        shortcodeText.select();
        document.execCommand('copy');

        var originalText = $(this).text();
        $(this).text('<?php echo esc_js(__('Copied!', 'whatsapp-order-button')); ?>');

        setTimeout(function() {
            $('#copy-shortcode').text(originalText);
        }, 2000);
    });

    // Initialize shortcode generator
    generateShortcode();

    // URL Template validation
    $('#custom_url_template').on('blur', function() {
        var template = $(this).val();
        if (template && !template.includes('{whatsapp_number}') && !template.includes('{encoded_message}')) {
            alert('<?php echo esc_js(__('Warning: Your custom URL template should include {whatsapp_number} and/or {encoded_message} placeholders.', 'whatsapp-order-button')); ?>');
        }
    });
});
</script>
